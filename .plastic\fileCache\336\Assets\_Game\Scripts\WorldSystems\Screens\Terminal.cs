/* MPV-STYLE MENU INTERFACE - FIXED VERSION
 * Fixed issues with options not displaying
 * Added comprehensive debugging
 */

using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Rendering.HighDefinition;
using TMPro;
using KinematicCharacterController.FPS;

public class Terminal : MonoBehaviour
{
    [Header("Setup")]
    public Camera playerCamera;
    public Transform playerTransform;
    public LayerMask screenLayerMask = -1;
    
    [Header("Terminal Settings")]
    public int maxVisibleOptions = 10;
    public float optionHeight = 30f;
    public int fontSize = 14;
    public Font terminalFont;
    public Color backgroundColor = Color.black;
    public Color defaultTextColor = Color.green;
    public Color inputTextColor = Color.white;
    public Color highlightColor = Color.white;
    public Color highlightTextColor = Color.black;
    
    [Header("Player Positioning")]
    public bool lockPlayerPosition = true;
    public Vector3 playerLockOffset = new Vector3(0, 0, -2f);
    public float lockTransitionSpeed = 2f;
    public bool lockPlayerRotation = true;
    
    [Header("Canvas Settings")]
    public ScreenFace screenFace = ScreenFace.Front;
    public float screenOffset = 0.01f;
    [SerializeField] private float pixelsPerUnit = 100f;
    
    [Header("Screen Light")]
    public bool enableScreenLight = true;
    public Vector2 lightSizeScale = Vector2.one;
    public float lightOffsetFromCanvas = 0.01f;
    public bool invertLightDirection = false;
    
    [Header("Terminal Behavior")]
    public KeyCode activateKey = KeyCode.E;
    public KeyCode exitKey = KeyCode.Escape;
    public KeyCode interactKey = KeyCode.F;
    
    [Header("Interaction System")]
    private FPSCharacterCamera cameraController;
    private FPSPlayerManager playerManager;
    private FPSCharacterController characterController;
    private bool isTerminalLocked = false;
    private bool wasPlayerMovementEnabled = true;
    
    [Header("Menu System")]
    public TerminalMenuData currentMenu;
    public TerminalMenuData rootMenu;
    private Stack<TerminalMenuData> menuStack = new Stack<TerminalMenuData>();
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public bool verboseDebugLogs = true; // Added for extra debugging
    
    // Private fields
    private Canvas worldCanvas;
    
    // Screen lighting
    private Light screenLight;
    private HDAdditionalLightData hdLight;
    private Vector3 baseCanvasScale;
    
    private GameObject backgroundObject;
    
    // MPV-style UI elements
    private TMP_InputField inputField;
    private GameObject inputFieldObject;
    private GameObject optionsContainer;
    private List<GameObject> optionObjects = new List<GameObject>();
    
    // Input handling
    private string currentFilter = "";
    private bool isTerminalActive = false;
    private int selectedIndex = 0;
    private List<TerminalMenuData.TerminalMenuItem> filteredItems = new List<TerminalMenuData.TerminalMenuItem>();
    
    // Player positioning
    private Vector3 originalPlayerPosition;
    private Quaternion originalPlayerRotation;
    private bool isPlayerLocked = false;
    private Vector3 targetPlayerPosition;
    private Quaternion targetPlayerRotation;
    
    // Distance checking
    public float activationDistance = 3f;
    private bool isPlayerInRange = false;
    
    // Screen look detection
    private bool isLookingAtScreen = false;
    private float lookAwayTimer = 0f;
    public float lookAwayUnlockDelay = 0.5f;
    
    public enum ScreenFace
    {
        Front, Back, Left, Right, Top, Bottom
    }
    
    void Start()
    {
        if (verboseDebugLogs) Debug.Log("[Terminal] Starting initialization...");
        
        SetupReferences();
        SetupWorldCanvas();
        SetupMPVInterface();
        
        // Initialize menu system with better error checking
        if (rootMenu != null)
        {
            currentMenu = rootMenu;
            Debug.Log($"[Terminal] Root menu loaded: '{rootMenu.menuTitle}' with {rootMenu.menuItems.Count} items");
            
            // Detailed item inspection
            for (int i = 0; i < rootMenu.menuItems.Count; i++)
            {
                var item = rootMenu.menuItems[i];
                Debug.Log($"[Terminal] Item {i}: '{item.displayName}' - Type: {item.itemType} - Color: {item.textColor} (Alpha: {item.textColor.a})");
                
                if (item.textColor.a == 0)
                {
                    Debug.LogWarning($"[Terminal] Menu item '{item.displayName}' has transparent text! Auto-fixing to use default color.");
                }
            }
        }
        else
        {
            Debug.LogError("[Terminal] CRITICAL: No root menu assigned! Please assign a TerminalMenuData ScriptableObject to the rootMenu field.");
        }
        
        // Validate TMP settings
        if (TMP_Settings.defaultFontAsset == null)
        {
            Debug.LogError("[Terminal] TMP default font asset is null! This will prevent text from rendering.");
        }
    }
    
    void SetupReferences()
    {
        if (playerTransform == null)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
        }
        
        if (playerCamera == null)
        {
            if (playerTransform != null)
            {
                playerCamera = playerTransform.GetComponentInChildren<Camera>();
            }
            
            if (playerCamera == null)
            {
                playerCamera = Camera.main;
            }
        }
        
        if (playerCamera != null)
        {
            cameraController = playerCamera.GetComponent<FPSCharacterCamera>();
        }
        
        playerManager = FindObjectOfType<FPSPlayerManager>();
        
        if (playerTransform != null)
        {
            characterController = playerTransform.GetComponent<FPSCharacterController>();
        }
    }
    
    void SetupWorldCanvas()
    {
        GameObject canvasGO = new GameObject("Terminal Canvas");
        canvasGO.transform.SetParent(transform, false);
        canvasGO.layer = gameObject.layer;
        
        worldCanvas = canvasGO.AddComponent<Canvas>();
        worldCanvas.renderMode = RenderMode.WorldSpace;
        worldCanvas.worldCamera = playerCamera;
        worldCanvas.sortingOrder = 10;
        worldCanvas.overrideSorting = true;
        
        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
        scaler.scaleFactor = 1f;
        
        GraphicRaycaster raycaster = canvasGO.AddComponent<GraphicRaycaster>();
        raycaster.blockingObjects = GraphicRaycaster.BlockingObjects.ThreeD;
        raycaster.blockingMask = screenLayerMask;
        raycaster.enabled = false; // No mouse interaction needed
        
        PositionCanvas();
        SetupScreenLight();
        
        canvasGO.SetActive(false); // Start inactive
        
        if (verboseDebugLogs) Debug.Log($"[Terminal] Canvas setup complete. Size: {worldCanvas.GetComponent<RectTransform>().sizeDelta}");
    }
    
    void PositionCanvas()
    {
        if (worldCanvas == null) return;
        
        RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
        if (canvasRect == null) return;
        
        Vector3 objectSize = GetObjectSize();
        Vector3 canvasPosition = Vector3.zero;
        Quaternion canvasRotation = Quaternion.identity;
        Vector2 canvasSize = Vector2.one;
        
        switch (screenFace)
        {
            case ScreenFace.Front:
                canvasSize = new Vector2(objectSize.x, objectSize.y);
                canvasPosition = new Vector3(0, 0, -(objectSize.z / 2f + screenOffset));
                canvasRotation = Quaternion.identity;
                break;
            case ScreenFace.Back:
                canvasSize = new Vector2(objectSize.x, objectSize.y);
                canvasPosition = new Vector3(0, 0, objectSize.z / 2f + screenOffset);
                canvasRotation = Quaternion.Euler(0, 180, 0);
                break;
            case ScreenFace.Left:
                canvasSize = new Vector2(objectSize.z, objectSize.y);
                canvasPosition = new Vector3(-(objectSize.x / 2f + screenOffset), 0, 0);
                canvasRotation = Quaternion.Euler(0, -90, 0);
                break;
            case ScreenFace.Right:
                canvasSize = new Vector2(objectSize.z, objectSize.y);
                canvasPosition = new Vector3(objectSize.x / 2f + screenOffset, 0, 0);
                canvasRotation = Quaternion.Euler(0, 90, 0);
                break;
            case ScreenFace.Top:
                canvasSize = new Vector2(objectSize.x, objectSize.z);
                canvasPosition = new Vector3(0, objectSize.y / 2f + screenOffset, 0);
                canvasRotation = Quaternion.Euler(90, 0, 0);
                break;
            case ScreenFace.Bottom:
                canvasSize = new Vector2(objectSize.x, objectSize.z);
                canvasPosition = new Vector3(0, -(objectSize.y / 2f + screenOffset), 0);
                canvasRotation = Quaternion.Euler(-90, 0, 0);
                break;
        }
        
        canvasSize = Vector2.Max(canvasSize, Vector2.one);
        float ppu = Mathf.Max(1f, pixelsPerUnit);
        canvasRect.sizeDelta = canvasSize * ppu;
        
        Vector3 properScale = new Vector3(1f / ppu, 1f / ppu, 1f / ppu);
        canvasRect.localScale = properScale;
        baseCanvasScale = properScale;
        
        canvasRect.localPosition = canvasPosition;
        canvasRect.localRotation = canvasRotation;
    }
    
    void SetupMPVInterface()
    {
        if (verboseDebugLogs) Debug.Log("[Terminal] Setting up MPV interface...");
        
        // Background
        CreateBackground();
        
        // Create centered input field
        CreateMPVInputField();
        
        // Create options display area
        CreateMPVOptionsDisplay();
        
        if (verboseDebugLogs) Debug.Log("[Terminal] MPV interface setup complete");
    }
    
    void SetupScreenLight()
    {
        if (!enableScreenLight || worldCanvas == null) return;

        if (screenLight == null)
        {
            GameObject lightGO = new GameObject("Terminal Area Light");
            lightGO.transform.SetParent(worldCanvas.transform, false);
            screenLight = lightGO.AddComponent<Light>();
            hdLight = lightGO.AddComponent<HDAdditionalLightData>();
            screenLight.type = LightType.Rectangle;
            screenLight.intensity = 0f;
        }

        RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
        Vector2 canvasMeters = canvasRect.sizeDelta * canvasRect.localScale.x;
        Vector2 lightSize = Vector2.Max(canvasMeters, Vector2.one * 0.01f);
        lightSize.x *= lightSizeScale.x;
        lightSize.y *= lightSizeScale.y;
        
        screenLight.type = LightType.Rectangle;
        hdLight.shapeWidth = lightSize.x;
        hdLight.shapeHeight = lightSize.y;

        Vector3 forward = worldCanvas.transform.forward;
        Vector3 canvasForward = invertLightDirection ? -forward : forward;
        screenLight.transform.localPosition = worldCanvas.transform.InverseTransformPoint(worldCanvas.transform.position + canvasForward * lightOffsetFromCanvas);
        screenLight.transform.rotation = Quaternion.LookRotation(canvasForward, worldCanvas.transform.up);
    }
    
    void CreateBackground()
    {
        backgroundObject = new GameObject("Background");
        backgroundObject.transform.SetParent(worldCanvas.transform, false);
        
        Image bg = backgroundObject.AddComponent<Image>();
        bg.color = backgroundColor;
        bg.raycastTarget = false;
        
        RectTransform bgRect = bg.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.sizeDelta = Vector2.zero;
        bgRect.anchoredPosition = Vector2.zero;
    }
    
    void CreateMPVInputField()
    {
        inputFieldObject = new GameObject("MPVInputField");
        inputFieldObject.transform.SetParent(worldCanvas.transform, false);
        
        RectTransform inputRect = inputFieldObject.AddComponent<RectTransform>();
        inputRect.anchorMin = new Vector2(0.1f, 0.45f);
        inputRect.anchorMax = new Vector2(0.9f, 0.55f);
        inputRect.sizeDelta = Vector2.zero;
        inputRect.anchoredPosition = Vector2.zero;
        
        // Input field background
        Image inputBg = inputFieldObject.AddComponent<Image>();
        inputBg.color = new Color(0.1f, 0.1f, 0.1f, 0.8f);
        
        // Create the actual input field
        inputField = inputFieldObject.AddComponent<TMP_InputField>();
        
        // Create text component for input
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(inputFieldObject.transform, false);
        
        RectTransform textRect = textGO.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = new Vector2(-20, 0); // Add padding
        textRect.anchoredPosition = Vector2.zero;
        
        TextMeshProUGUI inputText = textGO.AddComponent<TextMeshProUGUI>();
        
        // Try to get a valid font
        if (TMP_Settings.defaultFontAsset != null)
        {
            inputText.font = TMP_Settings.defaultFontAsset;
        }
        else
        {
            // Try to find any TMP font asset in the project
            TMP_FontAsset[] allFonts = Resources.FindObjectsOfTypeAll<TMP_FontAsset>();
            if (allFonts.Length > 0)
            {
                inputText.font = allFonts[0];
                Debug.LogWarning($"[Terminal] Using fallback font: {allFonts[0].name}");
            }
            else
            {
                Debug.LogError("[Terminal] No TMP fonts found! Text will not render.");
            }
        }
        
        inputText.fontSize = fontSize + 4;
        inputText.color = inputTextColor;
        inputText.alignment = TextAlignmentOptions.Center;
        
        inputField.textComponent = inputText;
        inputField.onValueChanged.AddListener(OnFilterChanged);
        inputField.caretColor = inputTextColor;
        
        inputFieldObject.SetActive(false);
    }
    
    void CreateMPVOptionsDisplay()
    {
        optionsContainer = new GameObject("MPVOptions");
        optionsContainer.transform.SetParent(worldCanvas.transform, false);
        
        RectTransform optionsRect = optionsContainer.AddComponent<RectTransform>();
        // Adjusted positioning - moved up slightly for better visibility
        optionsRect.anchorMin = new Vector2(0.1f, 0.1f);  // Raised from 0.05f
        optionsRect.anchorMax = new Vector2(0.9f, 0.43f); // Just below input field with small gap
        optionsRect.sizeDelta = Vector2.zero;
        optionsRect.anchoredPosition = Vector2.zero;
        
        // More visible background for debugging
        Image containerBg = optionsContainer.AddComponent<Image>();
        containerBg.color = new Color(0.1f, 0.1f, 0.1f, 0.5f); // More visible background
        
        VerticalLayoutGroup layout = optionsContainer.AddComponent<VerticalLayoutGroup>();
        layout.childAlignment = TextAnchor.UpperCenter;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandHeight = false;
        layout.childForceExpandWidth = true;
        layout.spacing = 2f;
        layout.padding = new RectOffset(10, 10, 5, 5);
        
        ContentSizeFitter fitter = optionsContainer.AddComponent<ContentSizeFitter>();
        fitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        
        // Make sure it starts hidden
        optionsContainer.SetActive(false);
        
        if (verboseDebugLogs) Debug.Log($"[Terminal] Options container created at anchors: {optionsRect.anchorMin} to {optionsRect.anchorMax}");
    }
    
    void Update()
    {
        CheckPlayerDistance();
        HandleInput();
        UpdatePlayerPosition();
    }
    
    void CheckPlayerDistance()
    {
        if (playerCamera == null) return;
        
        float distance = Vector3.Distance(playerCamera.transform.position, transform.position);
        bool inRange = distance <= activationDistance;
        
        if (inRange != isPlayerInRange)
        {
            isPlayerInRange = inRange;
            if (verboseDebugLogs && inRange) Debug.Log($"[Terminal] Player in range (distance: {distance:F2})");
        }
    }
    
    void HandleInput()
    {
        if (isPlayerInRange && !isTerminalLocked)
        {
            if (Input.GetKeyDown(interactKey))
            {
                if (verboseDebugLogs) Debug.Log("[Terminal] Interact key pressed - locking into terminal");
                LockIntoTerminal();
            }
        }
        
        if (isTerminalLocked)
        {
            UpdateScreenLookDetection();
            
            if (Input.GetKeyDown(exitKey))
            {
                if (menuStack.Count > 0)
                {
                    NavigateBack();
                }
                else
                {
                    UnlockFromTerminal();
                }
            }
            
            if (!isLookingAtScreen)
            {
                lookAwayTimer += Time.deltaTime;
                if (lookAwayTimer >= lookAwayUnlockDelay)
                {
                    UnlockFromTerminal();
                }
            }
            else
            {
                lookAwayTimer = 0f;
            }
            
            HandleMPVInput();
        }
        
        if (!isPlayerInRange && isTerminalLocked)
        {
            UnlockFromTerminal();
        }
    }
    
    void HandleMPVInput()
    {
        // Arrow key navigation
        if (Input.GetKeyDown(KeyCode.UpArrow))
        {
            int oldIndex = selectedIndex;
            selectedIndex = Mathf.Max(0, selectedIndex - 1);
            if (oldIndex != selectedIndex)
            {
                if (verboseDebugLogs) Debug.Log($"[Terminal] Selected index changed: {oldIndex} -> {selectedIndex}");
                UpdateOptionHighlight();
            }
        }
        else if (Input.GetKeyDown(KeyCode.DownArrow))
        {
            int oldIndex = selectedIndex;
            selectedIndex = Mathf.Min(filteredItems.Count - 1, selectedIndex + 1);
            if (oldIndex != selectedIndex)
            {
                if (verboseDebugLogs) Debug.Log($"[Terminal] Selected index changed: {oldIndex} -> {selectedIndex}");
                UpdateOptionHighlight();
            }
        }
        
        // Select with Enter
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            if (filteredItems.Count > 0 && selectedIndex < filteredItems.Count)
            {
                if (verboseDebugLogs) Debug.Log($"[Terminal] Selecting item: {filteredItems[selectedIndex].displayName}");
                SelectMenuItem(filteredItems[selectedIndex]);
            }
        }
        
        // Keep input field focused and handle text updates
        if (inputField != null)
        {
            if (!inputField.isFocused)
            {
                inputField.Select();
                inputField.ActivateInputField();
            }
            
            // Force update text display to prevent lag
            if (inputField.text != currentFilter)
            {
                OnFilterChanged(inputField.text);
            }
        }
    }
    
    void OnFilterChanged(string value)
    {
        currentFilter = value;
        selectedIndex = 0;
        if (verboseDebugLogs) Debug.Log($"[Terminal] Filter changed to: '{value}'");
        RefreshOptions();
    }
    
    void RefreshOptions()
    {
        if (verboseDebugLogs) Debug.Log($"[Terminal] RefreshOptions called. Current menu: {currentMenu?.menuTitle}, Items: {currentMenu?.menuItems?.Count}");
        
        // Clear existing options
        foreach (var obj in optionObjects)
        {
            if (obj != null) DestroyImmediate(obj);
        }
        optionObjects.Clear();
        
        if (currentMenu == null)
        {
            Debug.LogError("[Terminal] Current menu is null! Cannot refresh options.");
            return;
        }
        
        // Filter items based on input
        filteredItems.Clear();
        string filterLower = currentFilter.ToLower();
        
        foreach (var item in currentMenu.menuItems)
        {
            if (string.IsNullOrEmpty(currentFilter) || 
                item.displayName.ToLower().Contains(filterLower))
            {
                filteredItems.Add(item);
                if (verboseDebugLogs) Debug.Log($"[Terminal] Added filtered item: '{item.displayName}' (Type: {item.itemType})");
            }
        }
        
        // Add back option if in submenu
        if (menuStack.Count > 0 && currentMenu.showBackOption)
        {
            if (string.IsNullOrEmpty(currentFilter) || 
                currentMenu.backOptionText.ToLower().Contains(filterLower))
            {
                // Create a temporary back menu item
                var backItem = new TerminalMenuData.TerminalMenuItem
                {
                    displayName = currentMenu.backOptionText,
                    itemType = TerminalMenuData.MenuItemType.Action,
                    textColor = currentMenu.instructionColor
                };
                filteredItems.Add(backItem);
                if (verboseDebugLogs) Debug.Log("[Terminal] Added back option");
            }
        }
        
        Debug.Log($"[Terminal] Creating UI for {filteredItems.Count} filtered items");
        
        // Create option UI elements
        for (int i = 0; i < Mathf.Min(filteredItems.Count, maxVisibleOptions); i++)
        {
            CreateOptionUI(filteredItems[i], i);
        }
        
        // Force layout update - this is crucial!
        Canvas.ForceUpdateCanvases();
        LayoutRebuilder.ForceRebuildLayoutImmediate(optionsContainer.GetComponent<RectTransform>());
        
        // Update initial highlight
        UpdateOptionHighlight();
        
        // Log container state
        if (verboseDebugLogs)
        {
            Debug.Log($"[Terminal] Options container active: {optionsContainer.activeSelf}");
            Debug.Log($"[Terminal] Option objects created: {optionObjects.Count}");
            foreach (var obj in optionObjects)
            {
                if (obj != null)
                {
                    var text = obj.GetComponent<TextMeshProUGUI>();
                    Debug.Log($"[Terminal] Option: '{text?.text}' - Active: {obj.activeSelf} - Position: {obj.transform.localPosition}");
                }
            }
        }
    }
    
    void CreateOptionUI(TerminalMenuData.TerminalMenuItem item, int index)
    {
        // Create parent GameObject with background Image
        GameObject optionGO = new GameObject($"Option_{index}_{item.displayName}");
        optionGO.transform.SetParent(optionsContainer.transform, false);
        
        RectTransform rect = optionGO.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, optionHeight);
        
        // Background for selection highlighting
        Image bg = optionGO.AddComponent<Image>();
        bg.color = Color.clear;
        
        // Create child GameObject for text (Image and TextMeshProUGUI can't be on same GameObject)
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(optionGO.transform, false);
        
        RectTransform textRect = textGO.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;
        
        // Add text component to the child
        TextMeshProUGUI text = textGO.AddComponent<TextMeshProUGUI>();
        
        // Ensure we have a valid font
        if (TMP_Settings.defaultFontAsset != null)
        {
            text.font = TMP_Settings.defaultFontAsset;
        }
        else
        {
            // Try to find any TMP font asset in the project
            TMP_FontAsset[] allFonts = Resources.FindObjectsOfTypeAll<TMP_FontAsset>();
            if (allFonts.Length > 0)
            {
                text.font = allFonts[0];
                if (verboseDebugLogs) Debug.LogWarning($"[Terminal] Using fallback font: {allFonts[0].name}");
            }
            else
            {
                Debug.LogError("[Terminal] No TMP fonts found! Text will not render. Please import TextMeshPro essentials.");
            }
        }
        
        text.fontSize = fontSize;
        
        // Fix transparent colors
        Color textColor = item.textColor;
        if (textColor.a <= 0.01f)
        {
            textColor = defaultTextColor;
            if (verboseDebugLogs) Debug.Log($"[Terminal] Fixed transparent color for item '{item.displayName}'");
        }
        text.color = textColor;
        
        text.text = item.displayName;
        text.alignment = TextAlignmentOptions.Center;
        
        optionObjects.Add(optionGO);
        
        if (verboseDebugLogs) 
        {
            Debug.Log($"[Terminal] Created option UI: '{item.displayName}' at index {index} with color {text.color}");
        }
    }
    
    void UpdateOptionHighlight()
    {
        for (int i = 0; i < optionObjects.Count; i++)
        {
            if (optionObjects[i] == null) continue;
            
            Image bg = optionObjects[i].GetComponent<Image>();
            // Text is now on a child GameObject
            TextMeshProUGUI text = optionObjects[i].GetComponentInChildren<TextMeshProUGUI>();
            
            if (text == null)
            {
                Debug.LogError($"[Terminal] No text component found in option {i}!");
                continue;
            }
            
            if (i == selectedIndex)
            {
                bg.color = highlightColor;
                text.color = highlightTextColor;
                if (verboseDebugLogs) Debug.Log($"[Terminal] Highlighted option {i}: '{text.text}'");
            }
            else
            {
                bg.color = Color.clear;
                
                // Restore original color
                if (i < filteredItems.Count)
                {
                    var item = filteredItems[i];
                    Color textColor = item.textColor.a > 0.01f ? item.textColor : defaultTextColor;
                    text.color = textColor;
                }
            }
        }
    }
    
    void SelectMenuItem(TerminalMenuData.TerminalMenuItem item)
    {
        Debug.Log($"[Terminal] Selecting menu item: '{item.displayName}' (Type: {item.itemType})");
        
        // Check if this is the back option
        if (item.displayName == currentMenu.backOptionText && menuStack.Count > 0)
        {
            NavigateBack();
            return;
        }
        
        switch (item.itemType)
        {
            case TerminalMenuData.MenuItemType.Action:
                if (item.onActionTriggered != null && item.onActionTriggered.GetPersistentEventCount() > 0)
                {
                    Debug.Log($"[Terminal] Invoking action for '{item.displayName}'");
                    item.onActionTriggered?.Invoke();
                }
                else
                {
                    Debug.LogWarning($"[Terminal] No action configured for '{item.displayName}'");
                }
                // Clear input after action
                inputField.text = "";
                currentFilter = "";
                RefreshOptions();
                break;
                
            case TerminalMenuData.MenuItemType.Subdirectory:
                if (item.subMenu != null)
                {
                    NavigateToSubmenu(item.subMenu);
                }
                else
                {
                    Debug.LogWarning($"[Terminal] No submenu defined for '{item.displayName}'");
                }
                break;
                
            case TerminalMenuData.MenuItemType.Display:
                item.onActionTriggered?.Invoke();
                inputField.text = "";
                currentFilter = "";
                RefreshOptions();
                break;
        }
    }
    
    void NavigateToSubmenu(TerminalMenuData submenu)
    {
        Debug.Log($"[Terminal] Navigating to submenu: '{submenu.menuTitle}'");
        menuStack.Push(currentMenu);
        currentMenu = submenu;
        
        // Clear and refresh
        inputField.text = "";
        currentFilter = "";
        selectedIndex = 0;
        RefreshOptions();
    }
    
    void NavigateBack()
    {
        if (menuStack.Count > 0)
        {
            currentMenu = menuStack.Pop();
            Debug.Log($"[Terminal] Navigating back to: '{currentMenu.menuTitle}'");
            
            // Clear and refresh
            inputField.text = "";
            currentFilter = "";
            selectedIndex = 0;
            RefreshOptions();
        }
    }
    
    void UpdateScreenLookDetection()
    {
        if (playerCamera == null) return;
        
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit, activationDistance * 2f, screenLayerMask.value))
        {
            if (hit.collider.transform == transform || hit.collider.transform.IsChildOf(transform))
            {
                isLookingAtScreen = true;
            }
            else
            {
                isLookingAtScreen = false;
            }
        }
        else
        {
            isLookingAtScreen = false;
        }
    }
    
    void LockIntoTerminal()
    {
        isTerminalLocked = true;
        isTerminalActive = true;
        
        if (playerManager != null)
        {
            wasPlayerMovementEnabled = playerManager.enabled;
            playerManager.enabled = false;
        }
        
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
        
        ActivateTerminal();
    }
    
    void UnlockFromTerminal()
    {
        Debug.Log("[Terminal] Unlocking from terminal");
        isTerminalLocked = false;
        isTerminalActive = false;
        isLookingAtScreen = false;
        lookAwayTimer = 0f;
        
        if (playerManager != null && wasPlayerMovementEnabled)
        {
            playerManager.enabled = true;
        }
        
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        
        DeactivateTerminal();
    }
    
    void ActivateTerminal()
    {
        Debug.Log("[Terminal] === ACTIVATING TERMINAL ===");
        
        // Activate canvas
        if (worldCanvas != null)
        {
            worldCanvas.gameObject.SetActive(true);
            Debug.Log($"[Terminal] World canvas activated. Active in hierarchy: {worldCanvas.gameObject.activeInHierarchy}");
        }
        else
        {
            Debug.LogError("[Terminal] World canvas is null!");
        }
        
        // Activate input field
        if (inputFieldObject != null)
        {
            inputFieldObject.SetActive(true);
            inputField.Select();
            inputField.ActivateInputField();
            Debug.Log($"[Terminal] Input field activated. Active: {inputFieldObject.activeInHierarchy}");
        }
        else
        {
            Debug.LogError("[Terminal] Input field object is null!");
        }
        
        // CRITICAL: Activate the options container!
        if (optionsContainer != null)
        {
            optionsContainer.SetActive(true);
            Debug.Log($"[Terminal] Options container activated. Active: {optionsContainer.activeInHierarchy}");
            
            // Check container visibility
            RectTransform rect = optionsContainer.GetComponent<RectTransform>();
            Debug.Log($"[Terminal] Options container size: {rect.sizeDelta}, Scale: {rect.localScale}, Position: {rect.localPosition}");
        }
        else
        {
            Debug.LogError("[Terminal] Options container is null!");
        }
        
        if (lockPlayerPosition)
        {
            LockPlayer();
        }
        
        // Light turns on when terminal is active
        if (screenLight != null)
        {
            screenLight.intensity = 1f;
        }
        
        // Refresh the menu options
        RefreshOptions();
        
        // Final status check
        Debug.Log($"[Terminal] === ACTIVATION COMPLETE ===");
        Debug.Log($"[Terminal] Canvas active: {worldCanvas?.gameObject.activeInHierarchy}");
        Debug.Log($"[Terminal] Input active: {inputFieldObject?.activeInHierarchy}");
        Debug.Log($"[Terminal] Options active: {optionsContainer?.activeInHierarchy}");
        Debug.Log($"[Terminal] Option count: {optionObjects.Count}");
        Debug.Log($"[Terminal] Current menu: '{currentMenu?.menuTitle}' with {currentMenu?.menuItems?.Count} items");
    }
    
    void DeactivateTerminal()
    {
        isTerminalActive = false;
        
        if (worldCanvas != null)
        {
            worldCanvas.gameObject.SetActive(false);
        }
        
        if (optionsContainer != null)
        {
            optionsContainer.SetActive(false);
        }
        
        if (lockPlayerPosition)
        {
            UnlockPlayer();
        }
        
        // Light turns off
        if (screenLight != null)
        {
            screenLight.intensity = 0f;
        }
        
        // Clear input
        currentFilter = "";
        if (inputField != null)
        {
            inputField.text = "";
        }
        selectedIndex = 0;
    }
    
    void LockPlayer()
    {
        if (playerTransform == null) return;
        
        originalPlayerPosition = playerTransform.position;
        originalPlayerRotation = playerTransform.rotation;
        
        targetPlayerPosition = transform.position + transform.TransformDirection(playerLockOffset);
        
        if (lockPlayerRotation)
        {
            Vector3 directionToTerminal = (transform.position - targetPlayerPosition).normalized;
            targetPlayerRotation = Quaternion.LookRotation(directionToTerminal);
        }
        else
        {
            targetPlayerRotation = originalPlayerRotation;
        }
        
        isPlayerLocked = true;
    }
    
    void UnlockPlayer()
    {
        if (playerTransform == null) return;
        isPlayerLocked = false;
    }
    
    void UpdatePlayerPosition()
    {
        if (!isPlayerLocked || playerTransform == null) return;
        
        playerTransform.position = Vector3.Lerp(playerTransform.position, targetPlayerPosition, Time.deltaTime * lockTransitionSpeed);
        
        if (lockPlayerRotation)
        {
            playerTransform.rotation = Quaternion.Lerp(playerTransform.rotation, targetPlayerRotation, Time.deltaTime * lockTransitionSpeed);
        }
    }
    
    Vector3 GetObjectSize()
    {
        MeshFilter meshFilter = GetComponent<MeshFilter>();
        if (meshFilter != null && meshFilter.sharedMesh != null)
        {
            return Vector3.Scale(meshFilter.sharedMesh.bounds.size, transform.lossyScale);
        }
        
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            if (col is BoxCollider box)
                return Vector3.Scale(box.size, transform.lossyScale);
            else
                return col.bounds.size;
        }
        
        return transform.lossyScale;
    }
    
    Vector3 GetScreenForward()
    {
        switch (screenFace)
        {
            case ScreenFace.Front:
                return -transform.forward;
            case ScreenFace.Back:
                return transform.forward;
            case ScreenFace.Left:
                return transform.right;
            case ScreenFace.Right:
                return -transform.right;
            case ScreenFace.Top:
                return -transform.up;
            case ScreenFace.Bottom:
                return transform.up;
            default:
                return -transform.forward;
        }
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, activationDistance);
        
        if (worldCanvas != null)
        {
            Gizmos.color = Color.cyan;
            RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
            Vector3 canvasPos = transform.TransformPoint(canvasRect.localPosition);
            Gizmos.DrawWireCube(canvasPos, new Vector3(
                canvasRect.sizeDelta.x * canvasRect.localScale.x,
                canvasRect.sizeDelta.y * canvasRect.localScale.y,
                0.01f
            ));
        }
        
        if (showDebugInfo)
        {
            Vector3 size = GetObjectSize();
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(transform.position, size);
        }
        
        if (lockPlayerPosition && isTerminalActive)
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(targetPlayerPosition, 0.1f);
            Gizmos.DrawRay(targetPlayerPosition, targetPlayerRotation * Vector3.forward * 0.5f);
        }
        else if (lockPlayerPosition)
        {
            Gizmos.color = Color.green;
            Vector3 lockPos = transform.position + transform.TransformDirection(playerLockOffset);
            Gizmos.DrawWireSphere(lockPos, 0.5f);
            Gizmos.DrawLine(transform.position, lockPos);
        }
    }
    
    // Public API
    public void ExecuteCommand(string command)
    {
        // Not used in MPV style but kept for compatibility
    }
    
    public bool IsActive => isTerminalActive;
    
    public void SetTerminalEnabled(bool enabled)
    {
        if (worldCanvas != null)
        {
            worldCanvas.gameObject.SetActive(enabled);
        }
    }
    
    // Debug helper method
    public void TestMenuSystem()
    {
        Debug.Log("[Terminal] === MENU SYSTEM TEST ===");
        Debug.Log($"Root menu: {(rootMenu != null ? rootMenu.menuTitle : "NULL")}");
        Debug.Log($"Current menu: {(currentMenu != null ? currentMenu.menuTitle : "NULL")}");
        
        if (currentMenu != null)
        {
            Debug.Log($"Menu items count: {currentMenu.menuItems.Count}");
            foreach (var item in currentMenu.menuItems)
            {
                Debug.Log($"  - {item.displayName} (Type: {item.itemType}, Color Alpha: {item.textColor.a})");
            }
        }
        
        Debug.Log($"Options container: {(optionsContainer != null ? "EXISTS" : "NULL")}");
        Debug.Log($"Options container active: {(optionsContainer != null ? optionsContainer.activeSelf.ToString() : "N/A")}");
        Debug.Log($"Option objects count: {optionObjects.Count}");
    }
}
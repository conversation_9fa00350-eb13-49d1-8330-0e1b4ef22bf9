using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.Rendering.HighDefinition;

/// <summary>
/// Interactive Keypad Screen for PIN Entry
/// A polished, game-ready keypad interface for Unity
/// </summary>
[RequireComponent(typeof(Collider))]
public class InteractiveKeypadScreen : MonoBehaviour
{
    [Header("Setup")]
    [SerializeField] private Camera playerCamera;
    [SerializeField] private float interactionDistance = 3f;
    [SerializeField] private CrosshairManager crosshairManager;
    
    [Header("PIN Configuration")]
    [SerializeField] private string correctPIN = "1234";
    [SerializeField] private int maxPINLength = 4;
    [SerializeField] private bool showDigitsAsAsterisks = false;
    [SerializeField] private int maxAttempts = 3; // 0 = unlimited
    
    [Header("Feedback")]
    [SerializeField] private float feedbackDuration = 1.5f;
    [SerializeField] private Color successColor = new Color(0.2f, 0.8f, 0.2f, 1f);
    [SerializeField] private Color failureColor = new Color(0.8f, 0.2f, 0.2f, 1f);
    [SerializeField] private bool autoCloseOnSuccess = true;
    [SerializeField] private float autoCloseDelay = 2f;
    
    [Header("Input")]
    [SerializeField] private KeyCode interactKey = KeyCode.F;
    [SerializeField] private bool allowKeyboardInput = true;
    
    [Header("Performance")]
    [SerializeField] private bool useDistanceCulling = true;
    [SerializeField] private float visibilityDistance = 15f;
    [SerializeField] private LayerMask screenLayerMask = ~0;
    
    [Header("Screen Face")]
    [SerializeField] private ScreenFace screenFace = ScreenFace.Front;
    [SerializeField] private float screenOffset = 0.01f;
    
    [Header("Canvas")]
    [SerializeField] private float pixelsPerUnit = 300f;
    
    [Header("Visual Design")]
    [SerializeField] private Color backgroundColor = new Color(0.05f, 0.05f, 0.1f, 0.95f);
    [SerializeField] private Color displayBackgroundColor = new Color(0.02f, 0.02f, 0.05f, 1f);
    [SerializeField] private Color displayTextColor = new Color(0.2f, 0.9f, 0.9f, 1f);
    [SerializeField] private Color buttonNormalColor = new Color(0.1f, 0.15f, 0.2f, 0.9f);
    [SerializeField] private Color buttonHoverColor = new Color(0.2f, 0.3f, 0.4f, 1f);
    [SerializeField] private Color buttonPressedColor = new Color(0.3f, 0.5f, 0.6f, 1f);
    [SerializeField] private Color buttonTextColor = Color.white;
    [SerializeField] private Color specialButtonColor = new Color(0.15f, 0.1f, 0.2f, 0.9f);
    [SerializeField] private int buttonFontSize = 32;
    [SerializeField] private int displayFontSize = 36;
    
    [Header("Virtual Cursor")]
    [SerializeField] private Sprite cursorSpriteNormal;
    [SerializeField] private Sprite cursorSpritePointer;
    [SerializeField] private Vector2 cursorSize = new Vector2(32, 32);
    [SerializeField] private Color cursorColor = Color.yellow;
    [SerializeField] private bool enableClickScaleEffect = true;
    [SerializeField] private float clickScaleFactor = 0.9f;
    [SerializeField] private float clickScaleLerpSpeed = 20f;
    
    [Header("HDRP Screen Light")]
    [SerializeField] private bool enableScreenLight = false;
    [SerializeField] private float lightIntensity = 1000f;
    [SerializeField] private Color lightColor = new Color(0.5f, 0.7f, 1f, 1f);
    
    [Header("Events")]
    public UnityEngine.Events.UnityEvent<string> onCorrectPIN = new UnityEngine.Events.UnityEvent<string>();
    public UnityEngine.Events.UnityEvent<string> onIncorrectPIN = new UnityEngine.Events.UnityEvent<string>();
    public UnityEngine.Events.UnityEvent onMaxAttemptsReached = new UnityEngine.Events.UnityEvent();
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    
    public enum ScreenFace
    {
        Front, Back, Left, Right, Top, Bottom
    }
    
    // Private fields
    private Canvas worldCanvas;
    private GraphicRaycaster raycaster;
    private PointerEventData pointerData;
    private EventSystem eventSystem;
    private Image cursorImage;
    private RectTransform cursorRect;
    private GameObject lastHovered;
    private bool isLookingAtScreen = false;
    private bool wasLookingAtScreen = false;
    private bool isCanvasVisible = true;
    private bool ownsEventSystem = false;
    private Vector3 baseCanvasScale = Vector3.one;
    private bool isPointerCursorActive = false;
    private Vector3 cursorScaleTarget = Vector3.one;
    
    // Keypad specific
    private string currentPIN = "";
    private int attemptCount = 0;
    private bool isLocked = false;
    private Text displayText;
    private Text statusText;
    private GameObject displayPanel;
    private List<Button> numberButtons = new List<Button>();
    private Button clearButton;
    private Button enterButton;
    private Button backspaceButton;
    
    // HDRP light
    private Light screenLight;
    private HDAdditionalLightData hdLight;
    
    // Static caches
    private static Sprite s_defaultCursorSprite;
    private static Dictionary<Color32, Sprite> s_placeholderCache = new Dictionary<Color32, Sprite>();
    private static readonly List<RaycastResult> s_sharedRaycastResults = new List<RaycastResult>(16);
    
    void Start()
    {
        SetupReferences();
        SetupWorldCanvas();
        CreateKeypadUI();
        CreateVirtualCursor();
        SetupScreenLight();
    }
    
    void SetupReferences()
    {
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                Debug.LogError("InteractiveKeypadScreen: No camera found!");
                enabled = false;
                return;
            }
        }
        
        if (crosshairManager == null)
        {
#if UNITY_2023_1_OR_NEWER
            crosshairManager = FindFirstObjectByType<CrosshairManager>();
#else
            crosshairManager = FindObjectOfType<CrosshairManager>();
#endif
        }
        
        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();
            var inputSystemModuleType = System.Type.GetType("UnityEngine.InputSystem.UI.InputSystemUIInputModule, Unity.InputSystem");
            if (inputSystemModuleType != null)
            {
                eventSystemGO.AddComponent(inputSystemModuleType);
            }
            else
            {
                eventSystemGO.AddComponent<StandaloneInputModule>();
            }
            ownsEventSystem = true;
        }
        
        pointerData = new PointerEventData(eventSystem);
    }
    
    void SetupWorldCanvas()
    {
        GameObject canvasGO = new GameObject("Keypad Screen Canvas");
        canvasGO.transform.SetParent(transform, false);
        canvasGO.layer = gameObject.layer;
        
        worldCanvas = canvasGO.AddComponent<Canvas>();
        worldCanvas.renderMode = RenderMode.WorldSpace;
        worldCanvas.sortingOrder = 100;
        worldCanvas.worldCamera = playerCamera;
        
        raycaster = canvasGO.AddComponent<GraphicRaycaster>();
        raycaster.blockingObjects = GraphicRaycaster.BlockingObjects.None;
        raycaster.ignoreReversedGraphics = true;
        
        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
        scaler.scaleFactor = 1f;
        
        PositionCanvas(worldCanvas.GetComponent<RectTransform>());
    }
    
    void SetupScreenLight()
    {
        if (!enableScreenLight || worldCanvas == null)
            return;
        
        GameObject lightGO = new GameObject("Keypad Screen Light");
        lightGO.transform.SetParent(worldCanvas.transform, false);
        screenLight = lightGO.AddComponent<Light>();
        hdLight = lightGO.AddComponent<HDAdditionalLightData>();
        
        screenLight.type = LightType.Rectangle;
        screenLight.intensity = lightIntensity;
        screenLight.color = lightColor;
        
        RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
        Vector2 canvasMeters = canvasRect.sizeDelta * canvasRect.localScale.x;
        
        hdLight.shapeWidth = canvasMeters.x;
        hdLight.shapeHeight = canvasMeters.y;
        
        Vector3 forward = worldCanvas.transform.forward;
        screenLight.transform.localPosition = Vector3.zero;
        screenLight.transform.rotation = Quaternion.LookRotation(forward, worldCanvas.transform.up);
    }
    
    void PositionCanvas(RectTransform canvasRect)
    {
        Vector3 objectSize = GetObjectSize();
        Vector3 canvasPosition = Vector3.zero;
        Quaternion canvasRotation = Quaternion.identity;
        Vector2 canvasSize = Vector2.one;
        
        switch (screenFace)
        {
            case ScreenFace.Front:
                canvasSize = new Vector2(objectSize.x, objectSize.y);
                canvasPosition = new Vector3(0, 0, -(objectSize.z / 2f + screenOffset));
                canvasRotation = Quaternion.identity;
                break;
            case ScreenFace.Back:
                canvasSize = new Vector2(objectSize.x, objectSize.y);
                canvasPosition = new Vector3(0, 0, objectSize.z / 2f + screenOffset);
                canvasRotation = Quaternion.Euler(0, 180, 0);
                break;
            case ScreenFace.Left:
                canvasSize = new Vector2(objectSize.z, objectSize.y);
                canvasPosition = new Vector3(-(objectSize.x / 2f + screenOffset), 0, 0);
                canvasRotation = Quaternion.Euler(0, -90, 0);
                break;
            case ScreenFace.Right:
                canvasSize = new Vector2(objectSize.z, objectSize.y);
                canvasPosition = new Vector3(objectSize.x / 2f + screenOffset, 0, 0);
                canvasRotation = Quaternion.Euler(0, 90, 0);
                break;
            case ScreenFace.Top:
                canvasSize = new Vector2(objectSize.x, objectSize.z);
                canvasPosition = new Vector3(0, objectSize.y / 2f + screenOffset, 0);
                canvasRotation = Quaternion.Euler(90, 0, 0);
                break;
            case ScreenFace.Bottom:
                canvasSize = new Vector2(objectSize.x, objectSize.z);
                canvasPosition = new Vector3(0, -(objectSize.y / 2f + screenOffset), 0);
                canvasRotation = Quaternion.Euler(-90, 0, 0);
                break;
        }
        
        canvasSize = Vector2.Max(canvasSize, Vector2.one);
        
        float ppu = pixelsPerUnit;
        canvasRect.sizeDelta = canvasSize * ppu;
        baseCanvasScale = Vector3.one / ppu;
        canvasRect.localScale = baseCanvasScale;
        canvasRect.localPosition = canvasPosition;
        canvasRect.localRotation = canvasRotation;
    }
    
    void CreateKeypadUI()
    {
        RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
        Vector2 canvasSize = canvasRect.sizeDelta;
        
        // Background
        CreateBackground();
        
        // Display area (top 25% of screen)
        float displayHeight = canvasSize.y * 0.25f;
        CreateDisplayArea(canvasSize, displayHeight);
        
        // Keypad area (bottom 75% of screen)
        float keypadHeight = canvasSize.y * 0.75f;
        CreateKeypadButtons(canvasSize, keypadHeight, displayHeight);
    }
    
    void CreateBackground()
    {
        GameObject bgGO = new GameObject("Background");
        bgGO.transform.SetParent(worldCanvas.transform, false);
        Image bg = bgGO.AddComponent<Image>();
        bg.sprite = CreatePlaceholderSprite(backgroundColor);
        bg.color = backgroundColor;
        bg.raycastTarget = false;
        
        RectTransform bgRect = bg.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.sizeDelta = Vector2.zero;
        bgRect.anchoredPosition = Vector2.zero;
    }
    
    void CreateDisplayArea(Vector2 canvasSize, float displayHeight)
    {
        // Display panel background - centered and properly sized
        displayPanel = new GameObject("Display Panel");
        displayPanel.transform.SetParent(worldCanvas.transform, false);
        Image displayBg = displayPanel.AddComponent<Image>();
        displayBg.sprite = CreatePlaceholderSprite(displayBackgroundColor);
        displayBg.color = displayBackgroundColor;
        displayBg.raycastTarget = false;
        
        RectTransform displayRect = displayPanel.GetComponent<RectTransform>();
        displayRect.anchorMin = new Vector2(0.5f, 0.5f);
        displayRect.anchorMax = new Vector2(0.5f, 0.5f);
        float displayWidth = canvasSize.x * 0.6f; // 60% of canvas width
        displayRect.sizeDelta = new Vector2(displayWidth, displayHeight * 0.7f);
        displayRect.anchoredPosition = new Vector2(0, canvasSize.y * 0.3f); // Position at top portion
        
        // PIN display text
        GameObject displayTextGO = new GameObject("PIN Display");
        displayTextGO.transform.SetParent(displayPanel.transform, false);
        displayText = displayTextGO.AddComponent<Text>();
        displayText.text = "";
        
        // Better font loading
        Font font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        if (font == null)
        {
            font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        }
        if (font == null && Font.GetDefault() != null)
        {
            font = Font.GetDefault();
        }
        
        if (font != null)
        {
            displayText.font = font;
        }
        
        displayText.fontSize = displayFontSize;
        displayText.color = displayTextColor;
        displayText.alignment = TextAnchor.MiddleCenter;
        displayText.raycastTarget = false;
        
        RectTransform displayTextRect = displayText.GetComponent<RectTransform>();
        displayTextRect.anchorMin = new Vector2(0.1f, 0.3f);
        displayTextRect.anchorMax = new Vector2(0.9f, 0.7f);
        displayTextRect.sizeDelta = Vector2.zero;
        displayTextRect.anchoredPosition = Vector2.zero;
        
        // Status text (for feedback)
        GameObject statusTextGO = new GameObject("Status Text");
        statusTextGO.transform.SetParent(displayPanel.transform, false);
        statusText = statusTextGO.AddComponent<Text>();
        statusText.text = "Enter PIN";
        
        if (font != null)
        {
            statusText.font = font;
        }
        
        statusText.fontSize = Mathf.RoundToInt(displayFontSize * 0.6f);
        statusText.color = displayTextColor * 0.7f;
        statusText.alignment = TextAnchor.MiddleCenter;
        statusText.raycastTarget = false;
        
        RectTransform statusTextRect = statusText.GetComponent<RectTransform>();
        statusTextRect.anchorMin = new Vector2(0.1f, 0.05f);
        statusTextRect.anchorMax = new Vector2(0.9f, 0.3f);
        statusTextRect.sizeDelta = Vector2.zero;
        statusTextRect.anchoredPosition = Vector2.zero;
    }
    
    void CreateKeypadButtons(Vector2 canvasSize, float keypadHeight, float displayHeight)
    {
        // Button layout: 4 rows x 3 columns
        // Row 0-2: 1-9
        // Row 3: Clear, 0, Enter
        
        float buttonMargin = 0.02f; // 2% margin between buttons
        float sideMargin = 0.1f; // 10% margin on sides
        float topMargin = 0.05f; // 5% margin from display
        
        float availableWidth = canvasSize.x * (1f - 2f * sideMargin);
        float availableHeight = keypadHeight * (1f - topMargin);
        
        float buttonWidth = (availableWidth - 2f * buttonMargin * canvasSize.x) / 3f;
        float buttonHeight = (availableHeight - 3f * buttonMargin * canvasSize.y) / 4f;
        
        // Create number buttons 1-9
        for (int row = 0; row < 3; row++)
        {
            for (int col = 0; col < 3; col++)
            {
                int number = row * 3 + col + 1;
                float x = sideMargin * canvasSize.x + col * (buttonWidth + buttonMargin * canvasSize.x) + buttonWidth / 2f;
                float y = canvasSize.y * 0.65f - row * (buttonHeight + buttonMargin * canvasSize.y) - buttonHeight / 2f;
                
                Button btn = CreateKeypadButton(number.ToString(), new Vector2(x, y), new Vector2(buttonWidth, buttonHeight), false);
                numberButtons.Add(btn);
                
                // Add click handler
                int capturedNumber = number;
                btn.onClick.AddListener(() => OnNumberPressed(capturedNumber.ToString()));
            }
        }
        
        // Create bottom row buttons
        float bottomY = canvasSize.y * 0.65f - 3f * (buttonHeight + buttonMargin * canvasSize.y) - buttonHeight / 2f;
        
        // Clear button
        float clearX = sideMargin * canvasSize.x + buttonWidth / 2f;
        clearButton = CreateKeypadButton("CLR", new Vector2(clearX, bottomY), new Vector2(buttonWidth, buttonHeight), true);
        clearButton.onClick.AddListener(OnClearPressed);
        
        // 0 button
        float zeroX = sideMargin * canvasSize.x + (buttonWidth + buttonMargin * canvasSize.x) + buttonWidth / 2f;
        Button zeroBtn = CreateKeypadButton("0", new Vector2(zeroX, bottomY), new Vector2(buttonWidth, buttonHeight), false);
        numberButtons.Add(zeroBtn);
        zeroBtn.onClick.AddListener(() => OnNumberPressed("0"));
        
        // Enter button
        float enterX = sideMargin * canvasSize.x + 2f * (buttonWidth + buttonMargin * canvasSize.x) + buttonWidth / 2f;
        enterButton = CreateKeypadButton("ENT", new Vector2(enterX, bottomY), new Vector2(buttonWidth, buttonHeight), true);
        enterButton.onClick.AddListener(OnEnterPressed);
        
        // Optional: Add backspace button in top-right of keypad
        float backspaceX = canvasSize.x * 0.9f - buttonWidth * 0.3f;
        float backspaceY = canvasSize.y * 0.7f;
        backspaceButton = CreateKeypadButton("←", new Vector2(backspaceX, backspaceY), new Vector2(buttonWidth * 0.6f, buttonHeight * 0.6f), true);
        backspaceButton.onClick.AddListener(OnBackspacePressed);
    }
    
    Button CreateKeypadButton(string text, Vector2 position, Vector2 size, bool isSpecial)
    {
        GameObject buttonGO = new GameObject($"Button_{text}");
        buttonGO.transform.SetParent(worldCanvas.transform, false);
        
        Button button = buttonGO.AddComponent<Button>();
        Image buttonImage = buttonGO.AddComponent<Image>();
        
        Color normalColor = isSpecial ? specialButtonColor : buttonNormalColor;
        buttonImage.sprite = CreatePlaceholderSprite(normalColor);
        buttonImage.color = normalColor;
        buttonImage.raycastTarget = true;
        button.targetGraphic = buttonImage;
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = Vector2.zero;
        buttonRect.anchorMax = Vector2.zero;
        buttonRect.sizeDelta = size;
        buttonRect.anchoredPosition = position - worldCanvas.GetComponent<RectTransform>().sizeDelta / 2f;
        
        // Button text
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        Text buttonText = textGO.AddComponent<Text>();
        buttonText.text = text;
        buttonText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        buttonText.fontSize = buttonFontSize;
        buttonText.color = buttonTextColor;
        buttonText.alignment = TextAnchor.MiddleCenter;
        buttonText.raycastTarget = false;
        
        RectTransform textRect = buttonText.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;
        
        // Setup button states
        button.transition = Selectable.Transition.None;
        KeypadButtonHandler handler = buttonGO.AddComponent<KeypadButtonHandler>();
        handler.Initialize(normalColor, buttonHoverColor, buttonPressedColor, buttonImage);
        
        return button;
    }
    
    void CreateVirtualCursor()
    {
        GameObject cursorGO = new GameObject("Virtual Cursor");
        cursorGO.transform.SetParent(worldCanvas.transform, false);
        
        cursorImage = cursorGO.AddComponent<Image>();
        cursorImage.color = cursorColor;
        cursorImage.raycastTarget = false;
        
        cursorRect = cursorGO.GetComponent<RectTransform>();
        cursorRect.sizeDelta = cursorSize;
        cursorRect.pivot = new Vector2(0.5f, 0.5f);
        
        Sprite initial = cursorSpriteNormal ? cursorSpriteNormal : null;
        cursorImage.sprite = initial ?? CreateDefaultCursorSprite();
        
        cursorGO.transform.SetAsLastSibling();
        cursorGO.SetActive(false);
        
        cursorScaleTarget = Vector3.one;
    }
    
    void OnNumberPressed(string number)
    {
        if (isLocked) return;
        
        if (currentPIN.Length < maxPINLength)
        {
            currentPIN += number;
            UpdateDisplay();
            
            if (showDebugInfo)
                Debug.Log($"[Keypad] Number pressed: {number}, Current PIN: {currentPIN}");
        }
    }
    
    void OnClearPressed()
    {
        if (isLocked) return;
        
        currentPIN = "";
        UpdateDisplay();
        statusText.text = "Enter PIN";
        statusText.color = displayTextColor * 0.7f;
        
        if (showDebugInfo)
            Debug.Log("[Keypad] Clear pressed");
    }
    
    void OnBackspacePressed()
    {
        if (isLocked) return;
        
        if (currentPIN.Length > 0)
        {
            currentPIN = currentPIN.Substring(0, currentPIN.Length - 1);
            UpdateDisplay();
            
            if (showDebugInfo)
                Debug.Log($"[Keypad] Backspace pressed, Current PIN: {currentPIN}");
        }
    }
    
    void OnEnterPressed()
    {
        if (isLocked) return;
        if (string.IsNullOrEmpty(currentPIN)) return;
        
        attemptCount++;
        
        if (currentPIN == correctPIN)
        {
            OnSuccess();
        }
        else
        {
            OnFailure();
        }
    }
    
    void OnSuccess()
    {
        isLocked = true;
        statusText.text = "ACCESS GRANTED";
        statusText.color = successColor;
        displayText.color = successColor;
        
        if (showDebugInfo)
            Debug.Log($"[Keypad] Success! PIN: {currentPIN}");
        
        onCorrectPIN.Invoke(currentPIN);
        
        if (autoCloseOnSuccess)
        {
            StartCoroutine(AutoCloseCoroutine());
        }
    }
    
    void OnFailure()
    {
        statusText.text = "ACCESS DENIED";
        statusText.color = failureColor;
        
        if (showDebugInfo)
            Debug.Log($"[Keypad] Failed attempt {attemptCount}. Entered: {currentPIN}, Expected: {correctPIN}");
        
        onIncorrectPIN.Invoke(currentPIN);
        
        if (maxAttempts > 0 && attemptCount >= maxAttempts)
        {
            isLocked = true;
            statusText.text = "KEYPAD LOCKED";
            onMaxAttemptsReached.Invoke();
        }
        else
        {
            StartCoroutine(ResetAfterFailure());
        }
    }
    
    IEnumerator ResetAfterFailure()
    {
        isLocked = true;
        displayText.color = failureColor;
        
        yield return new WaitForSeconds(feedbackDuration);
        
        currentPIN = "";
        UpdateDisplay();
        displayText.color = displayTextColor;
        statusText.text = maxAttempts > 0 ? $"Attempt {attemptCount + 1}/{maxAttempts}" : "Enter PIN";
        statusText.color = displayTextColor * 0.7f;
        isLocked = false;
    }
    
    IEnumerator AutoCloseCoroutine()
    {
        yield return new WaitForSeconds(autoCloseDelay);
        SetScreenEnabled(false);
    }
    
    void UpdateDisplay()
    {
        if (showDigitsAsAsterisks && !string.IsNullOrEmpty(currentPIN))
        {
            displayText.text = new string('*', currentPIN.Length);
        }
        else
        {
            displayText.text = currentPIN;
        }
    }
    
    void Update()
    {
        if (playerCamera == null) return;
        
        float distance = Vector3.Distance(playerCamera.transform.position, transform.position);
        
        // Distance culling
        if (useDistanceCulling)
        {
            bool shouldBeVisible = distance <= visibilityDistance;
            if (shouldBeVisible != isCanvasVisible)
            {
                isCanvasVisible = shouldBeVisible;
                if (worldCanvas != null)
                {
                    worldCanvas.gameObject.SetActive(isCanvasVisible);
                }
                
                if (!isCanvasVisible && isLookingAtScreen)
                {
                    HideCursor();
                    isLookingAtScreen = false;
                }
            }
            
            if (!isCanvasVisible) return;
        }
        
        bool inRange = distance <= interactionDistance;
        
        if (inRange)
        {
            UpdateScreenInteraction();
            
            // Keyboard input when looking at screen
            if (isLookingAtScreen && allowKeyboardInput && !isLocked)
            {
                HandleKeyboardInput();
            }
        }
        else
        {
            if (isLookingAtScreen)
            {
                HideCursor();
                isLookingAtScreen = false;
            }
        }
        
        // Animate cursor click scale
        if (cursorRect != null && enableClickScaleEffect)
        {
            cursorRect.localScale = Vector3.Lerp(cursorRect.localScale, cursorScaleTarget, Time.deltaTime * clickScaleLerpSpeed);
        }
        
        // Notify crosshair manager
        if (wasLookingAtScreen != isLookingAtScreen)
        {
            if (crosshairManager != null)
            {
                crosshairManager.SetScreenLookState(isLookingAtScreen);
            }
            wasLookingAtScreen = isLookingAtScreen;
        }
    }
    
    void HandleKeyboardInput()
    {
        // Number keys
        for (int i = 0; i <= 9; i++)
        {
            if (Input.GetKeyDown(KeyCode.Alpha0 + i) || Input.GetKeyDown(KeyCode.Keypad0 + i))
            {
                OnNumberPressed(i.ToString());
            }
        }
        
        // Backspace
        if (Input.GetKeyDown(KeyCode.Backspace))
        {
            OnBackspacePressed();
        }
        
        // Enter
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            OnEnterPressed();
        }
        
        // Clear (Delete key)
        if (Input.GetKeyDown(KeyCode.Delete))
        {
            OnClearPressed();
        }
    }
    
    void UpdateScreenInteraction()
    {
        Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit, interactionDistance, screenLayerMask.value))
        {
            if (hit.collider.transform == transform || hit.collider.transform.IsChildOf(transform))
            {
                if (!isLookingAtScreen)
                {
                    ShowCursor();
                    isLookingAtScreen = true;
                }
                
                RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
                Vector3 screenPoint = playerCamera.WorldToScreenPoint(hit.point);
                Vector2 localPoint;
                if (RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRect, screenPoint, playerCamera, out localPoint))
                {
                    Vector2 half = canvasRect.sizeDelta * 0.5f;
                    localPoint.x = Mathf.Clamp(localPoint.x, -half.x, half.x);
                    localPoint.y = Mathf.Clamp(localPoint.y, -half.y, half.y);
                    cursorRect.anchoredPosition = localPoint;
                }
                
                HandleUIInteraction(screenPoint);
            }
            else
            {
                if (isLookingAtScreen)
                {
                    HideCursor();
                    isLookingAtScreen = false;
                }
            }
        }
        else
        {
            if (isLookingAtScreen)
            {
                HideCursor();
                isLookingAtScreen = false;
            }
        }
    }
    
    void HandleUIInteraction(Vector2 screenPosition)
    {
        s_sharedRaycastResults.Clear();
        
        RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
        Vector2 cursorCanvasPos = cursorRect.anchoredPosition;
        Vector3 cursorWorldPos = worldCanvas.transform.TransformPoint(new Vector3(cursorCanvasPos.x, cursorCanvasPos.y, 0));
        Vector3 cursorScreenPos = playerCamera.WorldToScreenPoint(cursorWorldPos);
        
        pointerData.position = cursorScreenPos;
        pointerData.delta = Vector2.zero;
        
        raycaster.Raycast(pointerData, s_sharedRaycastResults);
        
        GameObject newHover = null;
        RaycastResult hoverResult = default;
        if (s_sharedRaycastResults.Count > 0)
        {
            foreach (var result in s_sharedRaycastResults)
            {
                if (result.gameObject != cursorImage.gameObject && 
                    !result.gameObject.name.Contains("Background"))
                {
                    newHover = result.gameObject;
                    hoverResult = result;
                    break;
                }
            }
        }
        
        // Handle hover state changes
        if (newHover != lastHovered)
        {
            if (lastHovered != null)
            {
                ExecuteEvents.Execute(lastHovered, pointerData, ExecuteEvents.pointerExitHandler);
            }
            
            if (newHover != null)
            {
                ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerEnterHandler);
            }
            
            lastHovered = newHover;
            
            // Switch cursor sprite
            bool shouldUsePointer = newHover != null;
            if (shouldUsePointer != isPointerCursorActive)
            {
                isPointerCursorActive = shouldUsePointer;
                var desired = isPointerCursorActive ? 
                    (cursorSpritePointer ? cursorSpritePointer : cursorImage.sprite) : 
                    (cursorSpriteNormal ? cursorSpriteNormal : cursorImage.sprite);
                if (desired != null) cursorImage.sprite = desired;
            }
        }
        
        // Handle click
        if (Input.GetMouseButtonDown(0) || Input.GetKeyDown(interactKey))
        {
            if (newHover != null && !isLocked)
            {
                pointerData.pressPosition = cursorScreenPos;
                pointerData.pointerPressRaycast = hoverResult;
                pointerData.pointerPress = newHover;
                pointerData.button = PointerEventData.InputButton.Left;
                
                if (enableClickScaleEffect)
                {
                    cursorScaleTarget = Vector3.one * clickScaleFactor;
                }
                
                ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerDownHandler);
                
                if (Input.GetKeyDown(interactKey))
                {
                    ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerUpHandler);
                    ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerClickHandler);
                    
                    if (enableClickScaleEffect)
                    {
                        cursorScaleTarget = Vector3.one;
                    }
                    pointerData.pointerPress = null;
                }
            }
        }
        
        if (Input.GetMouseButtonUp(0))
        {
            if (pointerData.pointerPress != null)
            {
                ExecuteEvents.Execute(pointerData.pointerPress, pointerData, ExecuteEvents.pointerUpHandler);
                
                if (enableClickScaleEffect)
                {
                    cursorScaleTarget = Vector3.one;
                }
                
                if (pointerData.pointerPress == newHover && !isLocked)
                {
                    ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerClickHandler);
                }
                
                pointerData.pointerPress = null;
            }
        }
    }
    
    void ShowCursor()
    {
        if (cursorImage != null)
            cursorImage.gameObject.SetActive(true);
    }
    
    void HideCursor()
    {
        if (cursorImage != null)
            cursorImage.gameObject.SetActive(false);
        
        if (lastHovered != null)
        {
            ExecuteEvents.Execute(lastHovered, pointerData, ExecuteEvents.pointerExitHandler);
            lastHovered = null;
        }
    }
    
    Sprite CreateDefaultCursorSprite()
    {
        if (s_defaultCursorSprite != null)
            return s_defaultCursorSprite;
        
        int size = 64;
        Texture2D texture = new Texture2D(size, size);
        Color[] pixels = new Color[size * size];
        for (int i = 0; i < pixels.Length; i++) pixels[i] = Color.clear;
        
        int center = size / 2;
        int thickness = 2;
        int length = 20;
        int gap = 6;
        
        for (int i = 0; i < size; i++)
        {
            for (int j = 0; j < size; j++)
            {
                bool inHorizontal = Mathf.Abs(i - center) < thickness && Mathf.Abs(j - center) > gap && Mathf.Abs(j - center) < length;
                bool inVertical = Mathf.Abs(j - center) < thickness && Mathf.Abs(i - center) > gap && Mathf.Abs(i - center) < length;
                bool inCenter = Mathf.Abs(i - center) < thickness && Mathf.Abs(j - center) < thickness;
                if (inHorizontal || inVertical || inCenter)
                {
                    pixels[i * size + j] = Color.white;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.filterMode = FilterMode.Bilinear;
        texture.Apply();
        
        s_defaultCursorSprite = Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f), size);
        return s_defaultCursorSprite;
    }
    
    Sprite CreatePlaceholderSprite(Color color)
    {
        Color32 c32 = (Color32)color;
        if (s_placeholderCache.TryGetValue(c32, out var cached))
            return cached;
        
        Texture2D texture = new Texture2D(32, 32);
        Color[] pixels = new Color[32 * 32];
        
        for (int i = 0; i < pixels.Length; i++) 
            pixels[i] = color;
        
        texture.SetPixels(pixels);
        texture.filterMode = FilterMode.Bilinear;
        texture.Apply();
        
        var sprite = Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f), 32);
        s_placeholderCache[c32] = sprite;
        return sprite;
    }
    
    Vector3 GetObjectSize()
    {
        MeshFilter meshFilter = GetComponent<MeshFilter>();
        if (meshFilter != null && meshFilter.sharedMesh != null)
        {
            return Vector3.Scale(meshFilter.sharedMesh.bounds.size, transform.lossyScale);
        }
        
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            if (col is BoxCollider box)
                return Vector3.Scale(box.size, transform.lossyScale);
            else
                return col.bounds.size;
        }
        
        return transform.lossyScale;
    }
    
    // Public utility methods
    public void SetScreenEnabled(bool enabled)
    {
        if (worldCanvas != null)
        {
            worldCanvas.gameObject.SetActive(enabled);
        }
        isCanvasVisible = enabled;
        
        if (!enabled)
        {
            ResetKeypad();
        }
    }
    
    public void ResetKeypad()
    {
        currentPIN = "";
        attemptCount = 0;
        isLocked = false;
        UpdateDisplay();
        statusText.text = "Enter PIN";
        statusText.color = displayTextColor * 0.7f;
        displayText.color = displayTextColor;
    }
    
    public void SetCorrectPIN(string newPIN)
    {
        correctPIN = newPIN;
        ResetKeypad();
    }
    
    public bool IsUnlocked()
    {
        return !isLocked && currentPIN == correctPIN;
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionDistance);
        
        if (useDistanceCulling)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, visibilityDistance);
        }
        
        if (worldCanvas != null)
        {
            Gizmos.color = Color.cyan;
            RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
            Vector3 canvasPos = transform.TransformPoint(canvasRect.localPosition);
            Gizmos.DrawWireCube(canvasPos, new Vector3(
                canvasRect.sizeDelta.x * canvasRect.localScale.x,
                canvasRect.sizeDelta.y * canvasRect.localScale.y,
                0.01f
            ));
        }
    }
    
    void OnDestroy()
    {
        if (ownsEventSystem && eventSystem != null)
        {
            Destroy(eventSystem.gameObject);
        }
    }
}

// Helper component for button state handling
public class KeypadButtonHandler : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler, IPointerDownHandler, IPointerUpHandler
{
    private Color normalColor;
    private Color hoverColor;
    private Color pressedColor;
    private Image buttonImage;
    private bool isPressed = false;
    
    public void Initialize(Color normal, Color hover, Color pressed, Image image)
    {
        normalColor = normal;
        hoverColor = hover;
        pressedColor = pressed;
        buttonImage = image;
        buttonImage.color = normal;
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (!isPressed)
        {
            buttonImage.color = hoverColor;
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        if (!isPressed)
        {
            buttonImage.color = normalColor;
        }
    }
    
    public void OnPointerDown(PointerEventData eventData)
    {
        isPressed = true;
        buttonImage.color = pressedColor;
    }
    
    public void OnPointerUp(PointerEventData eventData)
    {
        isPressed = false;
        if (eventData.pointerEnter == gameObject)
        {
            buttonImage.color = hoverColor;
        }
        else
        {
            buttonImage.color = normalColor;
        }
    }
}
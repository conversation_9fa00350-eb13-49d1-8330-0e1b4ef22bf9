using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using UnityEngine.Rendering.HighDefinition;

/// <summary>
/// Doom 3 Style Interactive World Screen - Improved Version
/// </summary>
[RequireComponent(typeof(Collider))]
public class InteractiveWorldScreen : MonoBehaviour
{
    [Header("Setup")]
    [SerializeField] private Camera playerCamera;
    [SerializeField] private float interactionDistance = 3f;
    [SerializeField] private CrosshairManager crosshairManager;
    
    [Header("Input")]
    [SerializeField] private KeyCode interactKey = KeyCode.F;
    
    [Header("Performance")]
    [SerializeField] private bool useDistanceCulling = true;
    [SerializeField] private float visibilityDistance = 15f;
	[SerializeField] private LayerMask screenLayerMask = ~0;
    
    [Header("Screen Face")]
    [SerializeField] private ScreenFace screenFace = ScreenFace.Front;
    [SerializeField] private float screenOffset = 0.01f;
	
	[Header("Canvas")]
	[SerializeField] private float pixelsPerUnit = 300f;

	[Header("Visual Fidelity")]
	[SerializeField] private bool maintainPerceivedSize = false;
	[SerializeField] private float referenceDistance = 3f;
	[SerializeField] private bool adjustForFOV = true;
	[SerializeField] private float referenceVerticalFOV = 60f;
	[SerializeField] private bool pixelPerfectCursor = true;

	[Header("HDRP Screen Light")]
	[SerializeField] private bool enableScreenLight = false;
	[SerializeField] private Vector2 lightSizeScale = Vector2.one;
	[SerializeField] private float lightOffsetFromCanvas = 0.05f;
	[SerializeField] private float lightUpdateHz = 10f;
	[SerializeField, Range(0f,1f)] private float lightSmoothing = 0.8f;
	[SerializeField] private float intensityScale = 2000f;
	[SerializeField] private float minIntensity = 0f;
	[SerializeField] private float maxIntensity = 5000f;
	[SerializeField] private bool useAverageColor = true;
	[SerializeField] private Color fixedLightColor = Color.white;
	[SerializeField] private bool invertLightDirection = true;
	[SerializeField, Range(0f,1f)] private float textCoverageFactor = 0.25f;
    
    [Header("Virtual Cursor")]
    [SerializeField] private Sprite cursorSprite;
    [SerializeField] private Vector2 cursorSize = new Vector2(32, 32);
    [SerializeField] private Color cursorColor = Color.yellow;
	[SerializeField] private Sprite cursorSpriteNormal;
	[SerializeField] private Sprite cursorSpritePointer;
	[SerializeField] private bool enableClickScaleEffect = true;
	[SerializeField] private float clickScaleFactor = 0.9f;
	[SerializeField] private float clickScaleLerpSpeed = 20f;
    
    [Header("UI Design")]
    [SerializeField] private Color backgroundColor = new Color(0.05f, 0.05f, 0.1f, 0.95f);
    [SerializeField] private Sprite backgroundSprite;
    [SerializeField] private bool showBordersOnPlaceholders = false;
    
    [Header("UI Elements")]
    public List<ScreenButton> screenButtons = new List<ScreenButton>();
    public List<ScreenImage> screenImages = new List<ScreenImage>();
    public List<ScreenText> screenTexts = new List<ScreenText>();
    
    [Header("Debug")]
	[SerializeField] private bool showDebugInfo = false;
    [SerializeField] private bool forceVisible = true;
    
    public enum ScreenFace
    {
        Front, Back, Left, Right, Top, Bottom
    }
    
    [System.Serializable]
    public class ScreenButton
    {
        public string buttonName = "Button";
        public string buttonText = "Click Me";
        
        [Header("Position & Size (0-1 coordinates)")]
        [Range(0f, 1f)] public Vector2 anchorPosition = new Vector2(0.5f, 0.5f);
        [Range(0f, 1f)] public Vector2 size = new Vector2(0.3f, 0.1f);
        
        [Header("Sprites")]
        public Sprite normalSprite;
        public Sprite hoverSprite;
        public Sprite pressedSprite;
        
        [Header("Colors (if no sprites)")]
        public Color normalColor = new Color(0.1f, 0.2f, 0.2f, 0.8f);
        public Color hoverColor = new Color(0.2f, 0.4f, 0.4f, 1f);
        public Color pressedColor = new Color(0.4f, 0.6f, 0.6f, 1f);
        
        [Header("Text")]
        public int fontSize = 24;
        public Color textColor = Color.white;
        public Font customFont;
        
		[Header("Cooldown")]
		public float cooldownSeconds = 0f;
		[HideInInspector] public bool isCoolingDown;
		[HideInInspector] public float cooldownEndRealtime;

		[Header("Events")]
        public UnityEngine.Events.UnityEvent onClick = new UnityEngine.Events.UnityEvent();
        
        [HideInInspector] public Button buttonComponent;
        [HideInInspector] public Image imageComponent;
        [HideInInspector] public Text textComponent;
        [HideInInspector] public Rect editorRect;
    }
    
    [System.Serializable]
    public class ScreenImage
    {
        public string imageName = "Image";
        public Sprite sprite;
        
        [Header("Position & Size (0-1 coordinates)")]
        [Range(0f, 1f)] public Vector2 anchorPosition = new Vector2(0.5f, 0.5f);
        [Range(0f, 1f)] public Vector2 size = new Vector2(0.2f, 0.2f);
        
        public Color tint = Color.white;
        
        [Header("Rendering")]
        public FilterMode filterMode = FilterMode.Bilinear;
        
        [HideInInspector] public Image imageComponent;
        [HideInInspector] public Rect editorRect;
    }
    
    [System.Serializable]
    public class ScreenText
    {
        public string textName = "Text";
        public string content = "Sample Text";
        
        [Header("Position & Size (0-1 coordinates)")]
        [Range(0f, 1f)] public Vector2 anchorPosition = new Vector2(0.5f, 0.8f);
        [Range(0f, 1f)] public Vector2 size = new Vector2(0.8f, 0.1f);
        
        [Header("Text Settings")]
        public int fontSize = 30;
        public Color color = Color.cyan;
        public TextAnchor alignment = TextAnchor.MiddleCenter;
        public Font customFont;
        
        [HideInInspector] public Text textComponent;
        [HideInInspector] public Rect editorRect;
    }
    
    private Canvas worldCanvas;
    private GraphicRaycaster raycaster;
    private PointerEventData pointerData;
    private EventSystem eventSystem;
    private Image cursorImage;
    private RectTransform cursorRect;
    private GameObject lastHovered;
    private bool isLookingAtScreen = false;
    private bool wasLookingAtScreen = false;
    private bool isCanvasVisible = true;

	// Static caches to avoid per-instance allocations
	private static Sprite s_defaultCursorSprite;
    private static Dictionary<Color32, Sprite> s_placeholderCacheSolid = new Dictionary<Color32, Sprite>();
    private static Dictionary<Color32, Sprite> s_placeholderCacheBordered = new Dictionary<Color32, Sprite>();

	// Reusable list to avoid per-frame GC pressure
	private static readonly List<RaycastResult> s_sharedRaycastResults = new List<RaycastResult>(16);

	private bool ownsEventSystem = false;
	private Vector3 baseCanvasScale = Vector3.one;
	private bool isPointerCursorActive = false;
	private Vector3 cursorScaleTarget = Vector3.one;

	// HDRP light
	private Light screenLight;
	private HDAdditionalLightData hdLight;
	private float nextLightUpdateTime;
	private Color smoothedLightColor = Color.black;
	private float smoothedLightIntensity;
    
    void Start()
    {
        SetupReferences();
        SetupWorldCanvas();
        CreateUIElements();
        CreateVirtualCursor();
        SetupScreenLight();
    }
    
    void SetupReferences()
    {
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
            if (playerCamera == null)
            {
                Debug.LogError("No camera found!");
                enabled = false;
                return;
            }
        }
        
        if (crosshairManager == null)
        {
#if UNITY_2023_1_OR_NEWER
            crosshairManager = FindFirstObjectByType<CrosshairManager>();
#else
            crosshairManager = FindObjectOfType<CrosshairManager>();
#endif
        }
        
		eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
			eventSystem = eventSystemGO.AddComponent<EventSystem>();
			var inputSystemModuleType = System.Type.GetType("UnityEngine.InputSystem.UI.InputSystemUIInputModule, Unity.InputSystem");
			if (inputSystemModuleType != null)
			{
				eventSystemGO.AddComponent(inputSystemModuleType);
			}
			else
			{
				eventSystemGO.AddComponent<StandaloneInputModule>();
			}
			ownsEventSystem = true;
        }
        
        pointerData = new PointerEventData(eventSystem);
    }
    
    void SetupWorldCanvas()
    {
        GameObject canvasGO = new GameObject("World Screen Canvas");
        canvasGO.transform.SetParent(transform, false);
        canvasGO.layer = gameObject.layer;
        
        worldCanvas = canvasGO.AddComponent<Canvas>();
        worldCanvas.renderMode = RenderMode.WorldSpace;
        worldCanvas.sortingOrder = 100;
        worldCanvas.worldCamera = playerCamera;
        
	raycaster = canvasGO.AddComponent<GraphicRaycaster>();
	raycaster.blockingObjects = GraphicRaycaster.BlockingObjects.None;
	raycaster.ignoreReversedGraphics = true;
        
        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
        scaler.scaleFactor = 1f;
        
        PositionCanvas(worldCanvas.GetComponent<RectTransform>());
        SetupScreenLight();
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Canvas created for {gameObject.name}");
            Debug.Log($"[InteractiveWorldScreen] Canvas position: {canvasGO.transform.position}");
            Debug.Log($"[InteractiveWorldScreen] Canvas rotation: {canvasGO.transform.rotation.eulerAngles}");
            Debug.Log($"[InteractiveWorldScreen] Canvas scale: {canvasGO.transform.localScale}");
            Debug.Log($"[InteractiveWorldScreen] Canvas camera: {worldCanvas.worldCamera}");
        }
    }

	void SetupScreenLight()
	{
		if (!enableScreenLight || worldCanvas == null || playerCamera == null)
			return;

		// Create or find light
		if (screenLight == null)
		{
			GameObject lightGO = new GameObject("Screen Area Light");
			lightGO.transform.SetParent(worldCanvas.transform, false);
			screenLight = lightGO.AddComponent<Light>();
		hdLight = lightGO.AddComponent<HDAdditionalLightData>();
		screenLight.type = LightType.Rectangle;
		screenLight.intensity = 0f;
		}

		// Size and pose
		RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
		Vector2 canvasMeters = canvasRect.sizeDelta * canvasRect.localScale.x; // localScale is 1/ppu, so sizeDelta * scale == meters
		Vector2 lightSize = Vector2.Max(canvasMeters, Vector2.one * 0.01f);
		lightSize.x *= lightSizeScale.x;
		lightSize.y *= lightSizeScale.y;
		// In recent HDRP versions, Light.type controls area light; HD data exposes width/height
		screenLight.type = LightType.Rectangle;
		hdLight.shapeWidth = lightSize.x;
		hdLight.shapeHeight = lightSize.y;

		// Position slightly in front of canvas; allow inverting direction to match BACK-faced screens
		Vector3 forward = worldCanvas.transform.forward;
		Vector3 canvasForward = invertLightDirection ? -forward : forward;
		screenLight.transform.localPosition = worldCanvas.transform.InverseTransformPoint(worldCanvas.transform.position + canvasForward * lightOffsetFromCanvas);
		screenLight.transform.rotation = Quaternion.LookRotation(canvasForward, worldCanvas.transform.up);

		// (Removed GPU sampling path)
	}
    
    void PositionCanvas(RectTransform canvasRect)
    {
        Vector3 objectSize = GetObjectSize();
        Vector3 canvasPosition = Vector3.zero;
        Quaternion canvasRotation = Quaternion.identity;
        Vector2 canvasSize = Vector2.one;
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Object size: {objectSize}");
            Debug.Log($"[InteractiveWorldScreen] Screen face: {screenFace}");
        }
        
        switch (screenFace)
        {
            case ScreenFace.Front:
                canvasSize = new Vector2(objectSize.x, objectSize.y);
                canvasPosition = new Vector3(0, 0, -(objectSize.z / 2f + screenOffset));
                canvasRotation = Quaternion.identity;
                break;
            case ScreenFace.Back:
                canvasSize = new Vector2(objectSize.x, objectSize.y);
                canvasPosition = new Vector3(0, 0, objectSize.z / 2f + screenOffset);
                canvasRotation = Quaternion.Euler(0, 180, 0);
                break;
            case ScreenFace.Left:
                canvasSize = new Vector2(objectSize.z, objectSize.y);
                canvasPosition = new Vector3(-(objectSize.x / 2f + screenOffset), 0, 0);
                canvasRotation = Quaternion.Euler(0, -90, 0);
                break;
            case ScreenFace.Right:
                canvasSize = new Vector2(objectSize.z, objectSize.y);
                canvasPosition = new Vector3(objectSize.x / 2f + screenOffset, 0, 0);
                canvasRotation = Quaternion.Euler(0, 90, 0);
                break;
            case ScreenFace.Top:
                canvasSize = new Vector2(objectSize.x, objectSize.z);
                canvasPosition = new Vector3(0, objectSize.y / 2f + screenOffset, 0);
                canvasRotation = Quaternion.Euler(90, 0, 0);
                break;
            case ScreenFace.Bottom:
                canvasSize = new Vector2(objectSize.x, objectSize.z);
                canvasPosition = new Vector3(0, -(objectSize.y / 2f + screenOffset), 0);
                canvasRotation = Quaternion.Euler(-90, 0, 0);
                break;
        }
        
        // Make sure canvas is at least 1 unit in size
        canvasSize = Vector2.Max(canvasSize, Vector2.one);
        
		// Improved scaling - use larger pixels per unit for better visibility
		float ppu = pixelsPerUnit;
		canvasRect.sizeDelta = canvasSize * ppu;
		baseCanvasScale = Vector3.one / ppu;
		canvasRect.localScale = baseCanvasScale;
        canvasRect.localPosition = canvasPosition;
        canvasRect.localRotation = canvasRotation;
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Canvas size delta: {canvasRect.sizeDelta}");
            Debug.Log($"[InteractiveWorldScreen] Canvas local scale: {canvasRect.localScale}");
            Debug.Log($"[InteractiveWorldScreen] Canvas local position: {canvasRect.localPosition}");
        }
	}

	public float PixelsPerUnit => pixelsPerUnit;

	public Vector2 ComputeCanvasSizeUnits()
	{
		Vector3 objectSize = GetObjectSize();
		Vector2 canvasSize = Vector2.one;
		switch (screenFace)
		{
			case ScreenFace.Front:
				canvasSize = new Vector2(objectSize.x, objectSize.y);
				break;
			case ScreenFace.Back:
				canvasSize = new Vector2(objectSize.x, objectSize.y);
				break;
			case ScreenFace.Left:
				canvasSize = new Vector2(objectSize.z, objectSize.y);
				break;
			case ScreenFace.Right:
				canvasSize = new Vector2(objectSize.z, objectSize.y);
				break;
			case ScreenFace.Top:
				canvasSize = new Vector2(objectSize.x, objectSize.z);
				break;
			case ScreenFace.Bottom:
				canvasSize = new Vector2(objectSize.x, objectSize.z);
				break;
		}
		return Vector2.Max(canvasSize, Vector2.one);
	}

    // removed TMP-specific helpers per request
    
    void CreateUIElements()
    {
        CreateBackground();
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Creating {screenImages.Count} images, {screenTexts.Count} texts, {screenButtons.Count} buttons");
        }
        
        foreach (var img in screenImages)
            CreateImage(img);
        
        foreach (var txt in screenTexts)
            CreateText(txt);
        
        foreach (var btn in screenButtons)
            CreateButton(btn);
        
        // Add some default elements if lists are empty for testing
        if (forceVisible && screenButtons.Count == 0 && screenTexts.Count == 0 && screenImages.Count == 0)
        {
            if (showDebugInfo)
                Debug.Log("[InteractiveWorldScreen] No UI elements found, creating test elements");
            
            CreateTestElements();
        }
    }
    
    void CreateTestElements()
    {
        // Create a test button
        ScreenButton testButton = new ScreenButton();
        testButton.buttonName = "Test Button";
        testButton.buttonText = "TEST";
        testButton.anchorPosition = new Vector2(0.5f, 0.5f);
        testButton.size = new Vector2(0.3f, 0.1f);
        testButton.normalColor = Color.red;
        testButton.hoverColor = Color.green;
        testButton.pressedColor = Color.blue;
        testButton.textColor = Color.white;
        testButton.fontSize = 16;
        CreateButton(testButton);
        
        // Create test text
        ScreenText testText = new ScreenText();
        testText.textName = "Test Text";
        testText.content = "INTERACTIVE SCREEN";
        testText.anchorPosition = new Vector2(0.5f, 0.8f);
        testText.size = new Vector2(0.8f, 0.1f);
        testText.color = Color.cyan;
        testText.fontSize = 20;
        CreateText(testText);
        
        if (showDebugInfo)
            Debug.Log("[InteractiveWorldScreen] Test elements created");
    }
    
    void CreateBackground()
    {
        GameObject bgGO = new GameObject("Background");
        bgGO.transform.SetParent(worldCanvas.transform, false);
        Image bg = bgGO.AddComponent<Image>();
        
        if (backgroundSprite != null)
        {
            bg.sprite = backgroundSprite;
            bg.type = Image.Type.Sliced;
        }
        else
        {
            bg.sprite = CreatePlaceholderSprite(backgroundColor, FilterMode.Bilinear);
        }
        
        bg.color = backgroundColor;
		bg.raycastTarget = false;
        RectTransform bgRect = bg.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.sizeDelta = Vector2.zero;
        bgRect.anchoredPosition = Vector2.zero;
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Background created with color: {backgroundColor}");
            Debug.Log($"[InteractiveWorldScreen] Background size: {bgRect.sizeDelta}");
        }
    }
    
    void CreateButton(ScreenButton btnData)
    {
        GameObject buttonGO = new GameObject($"Button - {btnData.buttonName}");
        buttonGO.transform.SetParent(worldCanvas.transform, false);
        
        Button button = buttonGO.AddComponent<Button>();
        Image buttonImage = buttonGO.AddComponent<Image>();
        
        btnData.buttonComponent = button;
        btnData.imageComponent = buttonImage;
        
        // Ensure button is raycast target and interactable
        buttonImage.raycastTarget = true;
        button.interactable = true;
		button.targetGraphic = buttonImage;
        
        // Set sprite or color
        if (btnData.normalSprite != null)
        {
            buttonImage.sprite = btnData.normalSprite;
            buttonImage.type = Image.Type.Sliced;
        }
        else
        {
            buttonImage.sprite = CreatePlaceholderSprite(btnData.normalColor, FilterMode.Bilinear);
        }
        
        // Use the simpler positioning approach that works better for buttons
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = btnData.anchorPosition;
        buttonRect.anchorMax = btnData.anchorPosition;
        buttonRect.sizeDelta = worldCanvas.GetComponent<RectTransform>().sizeDelta * btnData.size;
        buttonRect.anchoredPosition = Vector2.zero;
        
        // Button text (legacy UI Text)
		GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        Text buttonText = textGO.AddComponent<Text>();
        btnData.textComponent = buttonText;
        
        buttonText.text = btnData.buttonText;
        buttonText.font = btnData.customFont ?? Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        buttonText.fontSize = btnData.fontSize;
        buttonText.color = btnData.textColor;
        buttonText.alignment = TextAnchor.MiddleCenter;
        
        // Text should NOT be raycast target to avoid blocking button interaction
        buttonText.raycastTarget = false;
        
        RectTransform textRect = buttonText.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;
        
        // Setup button states BEFORE adding click listener
        SetupButtonStates(button, btnData);
        
        // Add click listener
        button.onClick.AddListener(() => {
            if (showDebugInfo)
                Debug.Log($"[InteractiveWorldScreen] Button clicked: {btnData.buttonName}");
            btnData.onClick.Invoke();
        });
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Button created: {btnData.buttonName}");
            Debug.Log($"[InteractiveWorldScreen] Button anchor: {btnData.anchorPosition}, size multiplier: {btnData.size}");
            Debug.Log($"[InteractiveWorldScreen] Button final size: {buttonRect.sizeDelta}, font size: {btnData.fontSize}");
        }
    }
    
    void SetupButtonStates(Button button, ScreenButton btnData)
    {
        // Create a custom script to handle sprite swapping
        ButtonStateHandler handler = button.gameObject.AddComponent<ButtonStateHandler>();
        handler.Initialize(btnData);
        
		// Disable built-in tinting; we drive visuals manually
		button.transition = Selectable.Transition.None;
		// Keep colors neutral in case transition is changed later
		ColorBlock colors = button.colors;
		colors.normalColor = Color.white;
		colors.highlightedColor = Color.white;
		colors.pressedColor = Color.white;
		colors.selectedColor = Color.white;
		button.colors = colors;
    }
    
    void CreateImage(ScreenImage imgData)
    {
        GameObject imageGO = new GameObject($"Image - {imgData.imageName}");
        imageGO.transform.SetParent(worldCanvas.transform, false);
        
        Image img = imageGO.AddComponent<Image>();
        imgData.imageComponent = img;
        
        if (imgData.sprite != null)
        {
            img.sprite = imgData.sprite;
            // Apply filter mode to the sprite's texture if it exists
            if (imgData.sprite.texture != null)
            {
                imgData.sprite.texture.filterMode = imgData.filterMode;
            }
        }
        else
        {
            img.sprite = CreatePlaceholderSprite(new Color(0.5f, 0.5f, 0.5f, 0.8f), imgData.filterMode);
        }
        
		img.color = imgData.tint;
		img.raycastTarget = false;
        
        // Use the simpler positioning approach that works better for images
        RectTransform imgRect = img.GetComponent<RectTransform>();
        imgRect.anchorMin = imgData.anchorPosition;
        imgRect.anchorMax = imgData.anchorPosition;
        imgRect.sizeDelta = worldCanvas.GetComponent<RectTransform>().sizeDelta * imgData.size;
        imgRect.anchoredPosition = Vector2.zero;
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Image created: {imgData.imageName}");
            Debug.Log($"[InteractiveWorldScreen] Image anchor: {imgData.anchorPosition}, size multiplier: {imgData.size}");
            Debug.Log($"[InteractiveWorldScreen] Image final size: {imgRect.sizeDelta}");
        }
    }
    
    void CreateText(ScreenText txtData)
    {
        GameObject textGO = new GameObject($"Text - {txtData.textName}");
        textGO.transform.SetParent(worldCanvas.transform, false);
        
        Text txt = textGO.AddComponent<Text>();
        txtData.textComponent = txt;
        
        txt.text = txtData.content;
        txt.font = txtData.customFont ?? Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        txt.fontSize = txtData.fontSize;
        txt.color = txtData.color;
        txt.alignment = txtData.alignment;
		txt.raycastTarget = false;
        
        // Use the simpler positioning approach that works better for text
        RectTransform txtRect = txt.GetComponent<RectTransform>();
        txtRect.anchorMin = txtData.anchorPosition;
        txtRect.anchorMax = txtData.anchorPosition;
        txtRect.sizeDelta = worldCanvas.GetComponent<RectTransform>().sizeDelta * txtData.size;
        txtRect.anchoredPosition = Vector2.zero;
        
        if (showDebugInfo)
        {
            Debug.Log($"[InteractiveWorldScreen] Text created: {txtData.textName} - '{txtData.content}'");
            Debug.Log($"[InteractiveWorldScreen] Text anchor: {txtData.anchorPosition}, size multiplier: {txtData.size}");
            Debug.Log($"[InteractiveWorldScreen] Text final size: {txtRect.sizeDelta}, font size: {txtData.fontSize}");
        }
    }
    
    void CreateVirtualCursor()
    {
        GameObject cursorGO = new GameObject("Virtual Cursor");
        cursorGO.transform.SetParent(worldCanvas.transform, false);
        
        cursorImage = cursorGO.AddComponent<Image>();
        cursorImage.color = cursorColor;
        cursorImage.raycastTarget = false;
        
        cursorRect = cursorGO.GetComponent<RectTransform>();
        cursorRect.sizeDelta = cursorSize;
        cursorRect.pivot = new Vector2(0.5f, 0.5f);
        
		// Set initial sprite priority: explicit normal -> legacy single cursorSprite -> generated default
		Sprite initial = cursorSpriteNormal ? cursorSpriteNormal : (cursorSprite ? cursorSprite : null);
		cursorImage.sprite = initial ?? CreateDefaultCursorSprite();
        
        cursorGO.transform.SetAsLastSibling();
        cursorGO.SetActive(false);

		cursorScaleTarget = Vector3.one;
    }
    
    Sprite CreateDefaultCursorSprite()
    {
		if (s_defaultCursorSprite != null)
			return s_defaultCursorSprite;

		int size = 64;
		Texture2D texture = new Texture2D(size, size);
		Color[] pixels = new Color[size * size];
		for (int i = 0; i < pixels.Length; i++) pixels[i] = Color.clear;

		int center = size / 2;
		int thickness = 2;
		int length = 20;
		int gap = 6;

		for (int i = 0; i < size; i++)
		{
			for (int j = 0; j < size; j++)
			{
				bool inHorizontal = Mathf.Abs(i - center) < thickness && Mathf.Abs(j - center) > gap && Mathf.Abs(j - center) < length;
				bool inVertical = Mathf.Abs(j - center) < thickness && Mathf.Abs(i - center) > gap && Mathf.Abs(i - center) < length;
				bool inCenter = Mathf.Abs(i - center) < thickness && Mathf.Abs(j - center) < thickness;
				if (inHorizontal || inVertical || inCenter)
				{
					pixels[i * size + j] = Color.white;
				}
			}
		}

		texture.SetPixels(pixels);
		texture.filterMode = FilterMode.Bilinear;
		texture.Apply();

		s_defaultCursorSprite = Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f), size);
		return s_defaultCursorSprite;
    }
    
    Sprite CreatePlaceholderSprite(Color color, FilterMode filterMode = FilterMode.Bilinear)
    {
		Color32 c32 = (Color32)color;
		var cache = showBordersOnPlaceholders ? s_placeholderCacheBordered : s_placeholderCacheSolid;
		if (cache.TryGetValue(c32, out var cached))
			return cached;

		Texture2D texture = new Texture2D(32, 32);
		Color[] pixels = new Color[32 * 32];

		if (showBordersOnPlaceholders)
		{
			for (int i = 0; i < 32 * 32; i++)
			{
				int x = i % 32;
				int y = i / 32;
				if (x < 2 || x >= 30 || y < 2 || y >= 30)
					pixels[i] = Color.white * 0.5f;
				else
					pixels[i] = color;
			}

			texture.SetPixels(pixels);
			texture.filterMode = filterMode;
			texture.Apply();

			var sprite = Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f), 32, 4, SpriteMeshType.Tight, new Vector4(4, 4, 4, 4));
			cache[c32] = sprite;
			return sprite;
		}
		else
		{
			for (int i = 0; i < pixels.Length; i++) pixels[i] = color;

			texture.SetPixels(pixels);
			texture.filterMode = filterMode;
			texture.Apply();

			var sprite = Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f), 32);
			cache[c32] = sprite;
			return sprite;
		}
    }
    
    void Update()
    {
        if (playerCamera == null) return;
        
        float distance = Vector3.Distance(playerCamera.transform.position, transform.position);

		// Perceived-size scaling
		if (maintainPerceivedSize && worldCanvas != null)
		{
			float scaleByDistance = Mathf.Max(0.01f, distance / Mathf.Max(0.001f, referenceDistance));
			float fovFactor = 1f;
			if (adjustForFOV && playerCamera != null)
			{
				float vfov = playerCamera.orthographic ? referenceVerticalFOV : playerCamera.fieldOfView;
				fovFactor = Mathf.Tan(Mathf.Deg2Rad * referenceVerticalFOV * 0.5f) / Mathf.Tan(Mathf.Deg2Rad * vfov * 0.5f);
			}
			worldCanvas.transform.localScale = baseCanvasScale * scaleByDistance * fovFactor;
		}
        
        // Distance culling for performance - but don't throttle the actual interaction
        if (useDistanceCulling)
        {
            bool shouldBeVisible = distance <= visibilityDistance;
            if (shouldBeVisible != isCanvasVisible)
            {
                isCanvasVisible = shouldBeVisible;
                if (worldCanvas != null)
                {
                    worldCanvas.gameObject.SetActive(isCanvasVisible);
                }
                
                if (!isCanvasVisible && isLookingAtScreen)
                {
                    HideCursor();
                    isLookingAtScreen = false;
                }
            }
            
            if (!isCanvasVisible) return;
        }
        
        bool inRange = distance <= interactionDistance;
        
        if (inRange)
        {
            UpdateScreenInteraction();
        }
        else
        {
            if (isLookingAtScreen)
            {
                HideCursor();
                isLookingAtScreen = false;
            }
        }
        
		// Animate cursor click scale back to normal
		if (cursorRect != null && enableClickScaleEffect)
		{
			cursorRect.localScale = Vector3.Lerp(cursorRect.localScale, cursorScaleTarget, Time.deltaTime * clickScaleLerpSpeed);
		}

		// Notify crosshair manager of state change
        if (wasLookingAtScreen != isLookingAtScreen)
        {
            if (crosshairManager != null)
            {
                crosshairManager.SetScreenLookState(isLookingAtScreen);
            }
            wasLookingAtScreen = isLookingAtScreen;
        }

		// Light update
		UpdateScreenLight();
    }

	void UpdateScreenLight()
	{
		if (!enableScreenLight || worldCanvas == null || screenLight == null) return;
		if (Time.unscaledTime < nextLightUpdateTime) return;
		nextLightUpdateTime = Time.unscaledTime + (lightUpdateHz > 0f ? 1f / lightUpdateHz : 0.1f);

		Color avgColor = useAverageColor ? ComputeAverageColorFromUI() : fixedLightColor;

		float l = Mathf.Max(0.0001f, 0.2126f * avgColor.r + 0.7152f * avgColor.g + 0.0722f * avgColor.b);
		float target = Mathf.Clamp(l * intensityScale, minIntensity, maxIntensity);
		// Smooth
		smoothedLightColor = Color.Lerp(avgColor, smoothedLightColor, lightSmoothing);
		smoothedLightIntensity = Mathf.Lerp(target, smoothedLightIntensity, lightSmoothing);
		screenLight.color = smoothedLightColor;
		screenLight.intensity = smoothedLightIntensity;
	}



	Color ComputeAverageColorFromUI()
	{
		if (worldCanvas == null)
			return backgroundColor;

		RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
		Vector2 canvasSize = canvasRect.rect.size;
		float canvasArea = Mathf.Max(1f, canvasSize.x * canvasSize.y);

		Color accum = backgroundColor;
		float weight = 1f; // background covers full area

		// Images
		for (int i = 0; i < screenImages.Count; i++)
		{
			var data = screenImages[i];
			if (data == null) continue;
			var img = data.imageComponent;
			if (img == null || !img.isActiveAndEnabled) continue;
			RectTransform r = img.rectTransform;
			Vector2 s = r.rect.size;
			float areaFrac = Mathf.Clamp01((s.x * s.y) / canvasArea);
			Color c = img.color;
			float w = areaFrac * c.a;
			accum += new Color(c.r, c.g, c.b, 1f) * w;
			weight += w;
		}

		// Texts
		for (int i = 0; i < screenTexts.Count; i++)
		{
			var data = screenTexts[i];
			if (data == null) continue;
			var txt = data.textComponent;
			if (txt == null || !txt.isActiveAndEnabled) continue;
			RectTransform r = txt.rectTransform;
			Vector2 s = r.rect.size;
			float areaFrac = Mathf.Clamp01((s.x * s.y) / canvasArea) * textCoverageFactor;
			Color c = txt.color;
			float w = areaFrac * c.a;
			accum += new Color(c.r, c.g, c.b, 1f) * w;
			weight += w;
		}

		// Buttons (image + text)
		for (int i = 0; i < screenButtons.Count; i++)
		{
			var data = screenButtons[i];
			if (data == null) continue;
			var img = data.imageComponent;
			if (img != null && img.isActiveAndEnabled)
			{
				RectTransform r = img.rectTransform;
				Vector2 s = r.rect.size;
				float areaFrac = Mathf.Clamp01((s.x * s.y) / canvasArea);
				Color c = img.color;
				float w = areaFrac * c.a;
				accum += new Color(c.r, c.g, c.b, 1f) * w;
				weight += w;
			}
			var txt = data.textComponent;
			if (txt != null && txt.isActiveAndEnabled)
			{
				RectTransform r = txt.rectTransform;
				Vector2 s = r.rect.size;
				float areaFrac = Mathf.Clamp01((s.x * s.y) / canvasArea) * textCoverageFactor;
				Color c = txt.color;
				float w = areaFrac * c.a;
				accum += new Color(c.r, c.g, c.b, 1f) * w;
				weight += w;
			}
		}

		return accum / Mathf.Max(0.0001f, weight);
	}
    
    void UpdateScreenInteraction()
    {
		Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
		RaycastHit hit;
		
		if (Physics.Raycast(ray, out hit, interactionDistance, screenLayerMask.value))
        {
			if (hit.collider.transform == transform || hit.collider.transform.IsChildOf(transform))
            {
                if (!isLookingAtScreen)
                {
                    ShowCursor();
                    isLookingAtScreen = true;
                }
				
				// Convert hit point to canvas local coordinates using a robust method
				RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
				Vector3 screenPoint = playerCamera.WorldToScreenPoint(hit.point);
				Vector2 localPoint;
				if (RectTransformUtility.ScreenPointToLocalPointInRectangle(canvasRect, screenPoint, playerCamera, out localPoint))
				{
					// Snap cursor to pixel grid for crisper rendering if enabled
					if (pixelPerfectCursor)
					{
						localPoint.x = Mathf.Round(localPoint.x);
						localPoint.y = Mathf.Round(localPoint.y);
					}
					Vector2 half = canvasRect.sizeDelta * 0.5f;
					localPoint.x = Mathf.Clamp(localPoint.x, -half.x, half.x);
					localPoint.y = Mathf.Clamp(localPoint.y, -half.y, half.y);
					cursorRect.anchoredPosition = localPoint;
				}
				
				// Handle UI interaction
				HandleUIInteraction(screenPoint);
				
				if (showDebugInfo)
				{
					Debug.Log($"[UpdateScreenInteraction] Hit point: {hit.point}");
					Debug.Log($"[UpdateScreenInteraction] Cursor position: {cursorRect.anchoredPosition}");
				}
            }
            else
            {
                if (isLookingAtScreen)
                {
                    HideCursor();
                    isLookingAtScreen = false;
                }
            }
        }
        else
        {
            if (isLookingAtScreen)
            {
                HideCursor();
                isLookingAtScreen = false;
            }
        }
    }
    
	void HandleUIInteraction(Vector2 screenPosition)
    {
		// Clear previous results
		s_sharedRaycastResults.Clear();
		
        // Use the virtual cursor's position on the canvas instead of screen center
        Vector2 cursorCanvasPos = cursorRect.anchoredPosition;
        
        // Convert cursor canvas position to screen space for the event system
        // We need to simulate a screen position based on the cursor's canvas position
        RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
        
        // Convert canvas local position to world position
        Vector3 cursorWorldPos = worldCanvas.transform.TransformPoint(new Vector3(cursorCanvasPos.x, cursorCanvasPos.y, 0));
        
        // Convert world position to screen position for event system
        Vector3 cursorScreenPos = playerCamera.WorldToScreenPoint(cursorWorldPos);
        
        // Create pointer event data with the cursor's screen coordinates
        pointerData.position = cursorScreenPos;
        pointerData.delta = Vector2.zero;
        
        // Perform the raycast using the graphic raycaster
		raycaster.Raycast(pointerData, s_sharedRaycastResults);
        
        // Filter out the cursor itself from results
        GameObject newHover = null;
		RaycastResult hoverResult = default;
		if (s_sharedRaycastResults.Count > 0)
        {
			foreach (var result in s_sharedRaycastResults)
            {
                // Skip the cursor and background
                if (result.gameObject != cursorImage.gameObject && 
                    !result.gameObject.name.Contains("Background"))
                {
                    newHover = result.gameObject;
					hoverResult = result;
                    
                    if (showDebugInfo)
                        Debug.Log($"[HandleUIInteraction] Found UI element: {result.gameObject.name} at cursor position: {cursorCanvasPos}");
                    break;
                }
            }
        }
        
		// Handle hover state changes and cursor sprite
        if (newHover != lastHovered)
        {
            // Exit previous hover
            if (lastHovered != null)
            {
                ExecuteEvents.Execute(lastHovered, pointerData, ExecuteEvents.pointerExitHandler);
                if (showDebugInfo)
                    Debug.Log($"[HandleUIInteraction] Exiting hover: {lastHovered.name}");
            }
            
            // Enter new hover
            if (newHover != null)
            {
                ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerEnterHandler);
                if (showDebugInfo)
                    Debug.Log($"[HandleUIInteraction] Entering hover: {newHover.name}");
            }
            
            lastHovered = newHover;

			// Switch cursor sprite
			bool shouldUsePointer = newHover != null;
			if (shouldUsePointer != isPointerCursorActive)
			{
				isPointerCursorActive = shouldUsePointer;
				var desired = isPointerCursorActive ? (cursorSpritePointer ? cursorSpritePointer : cursorImage.sprite) : (cursorSpriteNormal ? cursorSpriteNormal : cursorImage.sprite);
				if (desired != null) cursorImage.sprite = desired;
			}
        }
        
        // Handle input
		if (Input.GetMouseButtonDown(0) || Input.GetKeyDown(interactKey))
        {
            if (newHover != null)
            {
                if (showDebugInfo)
                    Debug.Log($"[HandleUIInteraction] Click on: {newHover.name} at cursor pos: {cursorCanvasPos}");
                
                pointerData.pressPosition = cursorScreenPos;
				pointerData.pointerPressRaycast = hoverResult;
                pointerData.pointerPress = newHover;
				pointerData.button = PointerEventData.InputButton.Left;
				pointerData.clickCount = 1;
				pointerData.eligibleForClick = true;

				// Visual click scale
				if (enableClickScaleEffect)
				{
					cursorScaleTarget = Vector3.one * clickScaleFactor;
				}

				// Cooldown check for ScreenButton
				var pressButton = newHover.GetComponentInParent<Button>();
				if (pressButton != null)
				{
					var handler = pressButton.GetComponent<ButtonStateHandler>();
					if (handler != null && handler.HasActiveCooldown())
					{
						if (showDebugInfo)
							Debug.Log("[HandleUIInteraction] Click suppressed due to cooldown");
						return;
					}
				}
                
                ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerDownHandler);
                
                // For F key, complete the click immediately
                if (Input.GetKeyDown(interactKey))
                {
                    ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerUpHandler);
                    ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerClickHandler);
                    
                    // Arm cooldown after successful F key click
                    if (pressButton != null)
                    {
                        var handler = pressButton.GetComponent<ButtonStateHandler>();
                        if (handler != null) handler.ArmCooldown();
                    }
                    
                    // Reset click scale and pointer press
                    if (enableClickScaleEffect)
                    {
                        cursorScaleTarget = Vector3.one;
                    }
                    pointerData.pointerPress = null;
                }
            }
        }
        
        // Handle mouse button up (only for mouse input, F key does complete sequence immediately)
		if (Input.GetMouseButtonUp(0))
        {
            if (pointerData.pointerPress != null)
            {
                if (showDebugInfo)
                    Debug.Log($"[HandleUIInteraction] Mouse up on: {pointerData.pointerPress.name}");
                
                ExecuteEvents.Execute(pointerData.pointerPress, pointerData, ExecuteEvents.pointerUpHandler);

				// Release click scale
				if (enableClickScaleEffect)
				{
					cursorScaleTarget = Vector3.one;
				}
                
				// Check if we're still over the same object for click
                if (pointerData.pointerPress == newHover)
                {
					// Cooldown enforcement
					var pressButton = newHover.GetComponentInParent<Button>();
					bool allowClick = true;
					if (pressButton != null)
					{
						var handler = pressButton.GetComponent<ButtonStateHandler>();
						if (handler != null && handler.HasActiveCooldown())
						{
							allowClick = false;
						}
					}

					if (allowClick)
					{
						ExecuteEvents.Execute(newHover, pointerData, ExecuteEvents.pointerClickHandler);
						// Arm cooldown after a successful click
						if (pressButton != null)
						{
							var handler = pressButton.GetComponent<ButtonStateHandler>();
							if (handler != null) handler.ArmCooldown();
						}
					}
                    if (showDebugInfo)
                        Debug.Log($"[HandleUIInteraction] Mouse click executed on: {newHover.name}");
                }
                
                pointerData.pointerPress = null;
            }
        }
    }
    
    void ShowCursor()
    {
        if (cursorImage != null)
            cursorImage.gameObject.SetActive(true);
    }
    
    void HideCursor()
    {
        if (cursorImage != null)
            cursorImage.gameObject.SetActive(false);
        
        HandlePointerExit();
    }
    
    void HandlePointerExit()
    {
        if (lastHovered != null)
        {
            ExecuteEvents.Execute(lastHovered, pointerData, ExecuteEvents.pointerExitHandler);
            lastHovered = null;
        }
    }
    
    Vector3 GetObjectSize()
    {
        MeshFilter meshFilter = GetComponent<MeshFilter>();
        if (meshFilter != null && meshFilter.sharedMesh != null)
        {
            return Vector3.Scale(meshFilter.sharedMesh.bounds.size, transform.lossyScale);
        }
        
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            if (col is BoxCollider box)
                return Vector3.Scale(box.size, transform.lossyScale);
            else
                return col.bounds.size;
        }
        
        return transform.lossyScale;
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionDistance);
        
        if (useDistanceCulling)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, visibilityDistance);
        }
        
        if (worldCanvas != null)
        {
            Gizmos.color = Color.cyan;
            RectTransform canvasRect = worldCanvas.GetComponent<RectTransform>();
            Vector3 canvasPos = transform.TransformPoint(canvasRect.localPosition);
            Gizmos.DrawWireCube(canvasPos, new Vector3(
                canvasRect.sizeDelta.x * canvasRect.localScale.x,
                canvasRect.sizeDelta.y * canvasRect.localScale.y,
                0.01f
            ));
        }
        
        if (showDebugInfo)
        {
            Vector3 size = GetObjectSize();
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(transform.position, size);
        }
    }
    
    // Utility methods from the improved version
    public void SetScreenEnabled(bool enabled)
    {
        if (worldCanvas != null)
        {
            worldCanvas.gameObject.SetActive(enabled);
        }
        isCanvasVisible = enabled;
    }
    
    public void SetButtonEnabled(string buttonName, bool enabled)
    {
        var button = screenButtons.Find(b => b.buttonName == buttonName);
        if (button != null && button.buttonComponent != null)
        {
            button.buttonComponent.interactable = enabled;
        }
    }
    
    public void SetTextContent(string textName, string newContent)
    {
        var text = screenTexts.Find(t => t.textName == textName);
        if (text != null)
        {
            text.content = newContent;
            if (text.textComponent != null)
            {
                text.textComponent.text = newContent;
            }
        }
    }
    
    public void SetImageSprite(string imageName, Sprite newSprite)
    {
        var image = screenImages.Find(i => i.imageName == imageName);
        if (image != null)
        {
            image.sprite = newSprite;
            if (image.imageComponent != null)
            {
                image.imageComponent.sprite = newSprite;
                // Apply filter mode to the new sprite's texture if it exists
                if (newSprite != null && newSprite.texture != null)
                {
                    newSprite.texture.filterMode = image.filterMode;
                }
            }
        }
    }
    
    public void RefreshUI()
    {
        if (worldCanvas != null)
        {
            Destroy(worldCanvas.gameObject);
        }
        
        SetupWorldCanvas();
        CreateUIElements();
        CreateVirtualCursor();
    }
}

// Helper component for button state handling
public class ButtonStateHandler : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler, IPointerDownHandler, IPointerUpHandler
{
    private InteractiveWorldScreen.ScreenButton buttonData;
    private Image buttonImage;
    private bool isPressed = false;
    private bool isOnCooldown => buttonData != null && buttonData.isCoolingDown && Time.realtimeSinceStartup < buttonData.cooldownEndRealtime;
    
    public void Initialize(InteractiveWorldScreen.ScreenButton data)
    {
        buttonData = data;
        buttonImage = GetComponent<Image>();
		// Ensure we start in the true normal visual state
		SetNormalState();
    }

	private void SetNormalState()
	{
		if (buttonData.normalSprite != null)
		{
			buttonImage.sprite = buttonData.normalSprite;
			buttonImage.color = Color.white;
		}
		else
		{
			buttonImage.color = buttonData.normalColor;
		}
	}

    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (!isPressed)
        {
			if (buttonData.hoverSprite != null)
			{
				buttonImage.sprite = buttonData.hoverSprite;
				buttonImage.color = Color.white;
			}
			else
			{
				buttonImage.color = buttonData.hoverColor;
			}
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        if (!isPressed)
        {
			SetNormalState();
        }
    }
    
    public void OnPointerDown(PointerEventData eventData)
    {
        if (isOnCooldown)
        {
            return;
        }
        isPressed = true;
		if (buttonData.pressedSprite != null)
		{
			buttonImage.sprite = buttonData.pressedSprite;
			buttonImage.color = Color.white;
		}
		else
		{
			buttonImage.color = buttonData.pressedColor;
		}
    }
    
    public void OnPointerUp(PointerEventData eventData)
    {
        isPressed = false;
        // Return to hover state if still hovering
        if (eventData.pointerEnter == gameObject)
        {
			OnPointerEnter(eventData);
        }
        else
        {
			SetNormalState();
        }
    }

    public bool HasActiveCooldown()
    {
        return isOnCooldown;
    }

    public void ArmCooldown()
    {
        if (buttonData == null) return;
        if (buttonData.cooldownSeconds <= 0f) return;
        buttonData.isCoolingDown = true;
        buttonData.cooldownEndRealtime = Time.realtimeSinceStartup + buttonData.cooldownSeconds;
        // Optionally dim while cooling down
        if (buttonData.normalSprite != null)
        {
            buttonImage.color = new Color(1f, 1f, 1f, 0.7f);
        }
        else
        {
            buttonImage.color = Color.Lerp(buttonData.normalColor, Color.black, 0.2f);
        }
    }
}
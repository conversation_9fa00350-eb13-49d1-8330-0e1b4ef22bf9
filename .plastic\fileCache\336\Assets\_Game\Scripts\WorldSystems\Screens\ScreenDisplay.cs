using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;

// Minimal, drop-in placeholder screen that reuses all functionality from InteractiveWorldScreen
// It only prepares a single monospaced-like status text before the base class builds the UI.
[DisallowMultipleComponent]
[RequireComponent(typeof(Collider))]
public class ScreenDisplay : InteractiveWorldScreen
{
    [Header("Power State")]
    [SerializeField] private bool powered = true;
    [SerializeField] private string offText = "NO POWER\n----------------------------------------";
    [SerializeField] private Color offTextColor = new Color(0.6f, 0.6f, 0.6f, 1f);

    [Header("Placeholder Settings")]
    [SerializeField] private string unitId = "XMR-MINER-734";
    [TextArea(10, 24)]
    [SerializeField] private string placeholderText = string.Empty;
    [SerializeField] private int placeholderFontSize = 18;
    [SerializeField] private Color placeholderColor = new Color(0.85f, 1f, 0.95f, 1f);
    [Tooltip("Uniform margin around the text content as a fraction of the canvas size (0-0.45).")]
    [Range(0f, 0.45f)]
    [SerializeField] private float uniformMargin = 0.03f;

    [Header("Button Events")] 
    [SerializeField] private UnityEvent onWithdraw;
    [SerializeField] private UnityEvent onDiagnostics;
    [SerializeField] private UnityEvent onShutdown;

    [Header("Button Style (applies to all 3)")]
    [SerializeField] private Sprite buttonNormalSprite;
    [SerializeField] private Sprite buttonHoverSprite;
    [SerializeField] private Sprite buttonPressedSprite;
    [SerializeField] private Color buttonNormalColor = new Color(0.1f, 0.2f, 0.2f, 0.8f);
    [SerializeField] private Color buttonHoverColor = new Color(0.2f, 0.4f, 0.4f, 1f);
    [SerializeField] private Color buttonPressedColor = new Color(0.4f, 0.6f, 0.6f, 1f);
    [SerializeField] private Color buttonTextColor = Color.white;
    [SerializeField] private Font buttonFont;

    // Prepare the lists used by the base class BEFORE its Start() runs
    // Awake is guaranteed to execute before Start, so the base CreateUIElements() will use our data
    protected void Awake()
    {
        // Cache initial text so we can restore it when power returns
        string originalPlaceholder = placeholderText;
        // Clear any preconfigured elements so this screen acts as a no-customization placeholder
        if (screenButtons != null) screenButtons.Clear();
        if (screenImages != null) screenImages.Clear();
        if (screenTexts != null) screenTexts.Clear();

        // Compose default placeholder if none supplied in inspector
        if (string.IsNullOrEmpty(placeholderText))
        {
            placeholderText = $@"{unitId}
----------------------------------------
< UNIT: XMR-MINER-734 >
< STATUS: ONLINE / PROCESSING          >
----------------------------------------
SYS INTEGRITY: [███████░░░░░░░░░░] 87.4%
THERMAL:       68°C
POWER DRAW:    4.12 kW
EFFICIENCY:    92.1%

YIELD BUFFER:  [147.23] credits

----------------------------------------";
        }

        // Compute content rect with uniform margin (normalized 0..1)
        float m = Mathf.Clamp01(uniformMargin);
        Vector2 contentSize = new Vector2(Mathf.Clamp01(1f - 2f * m), Mathf.Clamp01(1f - 2f * m));

        // Add a single text block with margin
        var txt = new InteractiveWorldScreen.ScreenText
        {
            textName = "Status",
            content = placeholderText,
            fontSize = placeholderFontSize,
            color = placeholderColor,
            alignment = TextAnchor.UpperLeft,
            anchorPosition = new Vector2(0.5f, 0.5f),
            size = contentSize
        };
        screenTexts.Add(txt);

        // Place three action buttons where the last line controls used to be: inside the text block area
        // Make their size derive from the text font size so they scale with the text, not fill the whole bottom row
        // We approximate character metrics to compute a compact width per word (monospace-like)
        Vector2 canvasUnits = ComputeCanvasSizeUnits();
        float ppu = PixelsPerUnit;
        Vector2 canvasPx = canvasUnits * ppu;
        // Guard against degenerate canvas
        if (canvasPx.x <= 0f) canvasPx.x = 1024f;
        if (canvasPx.y <= 0f) canvasPx.y = 768f;

        float lineHeightPx = placeholderFontSize * 1.25f;  // rough line-height multiplier
        float charWidthPx = placeholderFontSize * 0.62f;   // rough average character width
        float horizPadPx = placeholderFontSize * 0.6f;     // horizontal padding inside each button
        float vertPadPx = placeholderFontSize * 0.35f;     // vertical padding inside each button
        float gapPx = placeholderFontSize * 1.0f;          // gap between buttons ~ one space
        float indentPx = placeholderFontSize * 0.8f;       // small left indent from text block

        float btnHNorm = Mathf.Clamp01((lineHeightPx + vertPadPx) / canvasPx.y);

        float WidthNormForLabel(string label)
        {
            float wPx = (label.Length * charWidthPx) + (horizPadPx * 2f);
            return Mathf.Clamp01(wPx / canvasPx.x);
        }

        // Text block rect in normalized canvas space
        float left = 0.5f - contentSize.x * 0.5f;
        float bottom = 0.5f - contentSize.y * 0.5f;
        float indentNorm = Mathf.Clamp01(indentPx / canvasPx.x);
        float gapNorm = Mathf.Clamp01(gapPx / canvasPx.x);
        float yCenter = bottom + (btnHNorm * 0.5f) + Mathf.Clamp01((vertPadPx * 0.25f) / canvasPx.y);

        string w1 = "WITHDRAW";
        string w2 = "DIAGNOSTICS";
        string w3 = "SHUTDOWN";

        float w1Norm = WidthNormForLabel(w1);
        float w2Norm = WidthNormForLabel(w2);
        float w3Norm = WidthNormForLabel(w3);

        float x1 = left + indentNorm + (w1Norm * 0.5f);
        float x2 = x1 + (w1Norm * 0.5f) + gapNorm + (w2Norm * 0.5f);
        float x3 = x2 + (w2Norm * 0.5f) + gapNorm + (w3Norm * 0.5f);

        // Helper to apply the shared style
        void ApplyStyle(InteractiveWorldScreen.ScreenButton b)
        {
            b.fontSize = placeholderFontSize;
            b.textColor = buttonTextColor;
            b.customFont = buttonFont;
            if (buttonNormalSprite != null || buttonHoverSprite != null || buttonPressedSprite != null)
            {
                b.normalSprite = buttonNormalSprite;
                b.hoverSprite = buttonHoverSprite;
                b.pressedSprite = buttonPressedSprite;
            }
            else
            {
                b.normalColor = buttonNormalColor;
                b.hoverColor = buttonHoverColor;
                b.pressedColor = buttonPressedColor;
            }
        }

        var bWithdraw = new InteractiveWorldScreen.ScreenButton
        {
            buttonName = "Withdraw",
            buttonText = w1,
            anchorPosition = new Vector2(x1, yCenter),
            size = new Vector2(w1Norm, btnHNorm)
        };
        ApplyStyle(bWithdraw);
        bWithdraw.onClick.AddListener(() => onWithdraw?.Invoke());
        screenButtons.Add(bWithdraw);

        var bDiag = new InteractiveWorldScreen.ScreenButton
        {
            buttonName = "Diagnostics",
            buttonText = w2,
            anchorPosition = new Vector2(x2, yCenter),
            size = new Vector2(w2Norm, btnHNorm)
        };
        ApplyStyle(bDiag);
        bDiag.onClick.AddListener(() => onDiagnostics?.Invoke());
        screenButtons.Add(bDiag);

        var bShutdown = new InteractiveWorldScreen.ScreenButton
        {
            buttonName = "Shutdown",
            buttonText = w3,
            anchorPosition = new Vector2(x3, yCenter),
            size = new Vector2(w3Norm, btnHNorm)
        };
        ApplyStyle(bShutdown);
        bShutdown.onClick.AddListener(() => onShutdown?.Invoke());
        screenButtons.Add(bShutdown);
    }

    // Optional helper to update the placeholder at runtime
    public void SetPlaceholder(string content)
    {
        // Always keep the latest content so we can restore it when power comes back
        placeholderText = content;

        if (screenTexts == null)
            return;
        if (screenTexts.Count == 0)
            screenTexts.Add(new InteractiveWorldScreen.ScreenText());

        // If powered off, keep showing the OFF text and just cache the content
        if (!powered)
        {
            return;
        }

        // Update the live text when powered on
        screenTexts[0].content = content;
        if (screenTexts[0].textComponent != null)
            screenTexts[0].textComponent.text = content;
    }

    // Power control: switch between ON and OFF visual states without disabling the screen object
    public void SetPowered(bool isOn)
    {
        powered = isOn;
        try
        {
            if (screenTexts == null || screenTexts.Count == 0)
                return;

            if (powered)
            {
                // Restore placeholder content and color
                screenTexts[0].content = placeholderText;
                if (screenTexts[0].textComponent != null)
                {
                    screenTexts[0].textComponent.text = placeholderText;
                    screenTexts[0].textComponent.color = placeholderColor;
                }
                // Re-enable buttons if present
                var btns = GetComponentsInChildren<Button>(true);
                foreach (var b in btns) { b.interactable = true; }
            }
            else
            {
                // Show OFF message and dim color
                screenTexts[0].content = offText;
                if (screenTexts[0].textComponent != null)
                {
                    screenTexts[0].textComponent.text = offText;
                    screenTexts[0].textComponent.color = offTextColor;
                }
                // Disable buttons while off
                var btns = GetComponentsInChildren<Button>(true);
                foreach (var b in btns) { b.interactable = false; }
            }
        }
        catch { }
    }

    // Optional helper to update uniform margin at runtime and rebuild UI
    public void SetUniformMargin(float margin)
    {
        uniformMargin = Mathf.Clamp(margin, 0f, 0.45f);
        RefreshUI();
    }

    // Expose event binding for code wiring
    public void AddWithdrawListener(UnityAction action)
    {
        if (action != null)
            onWithdraw.AddListener(action);
    }
    public void AddDiagnosticsListener(UnityAction action)
    {
        if (action != null)
            onDiagnostics.AddListener(action);
    }
    public void AddShutdownListener(UnityAction action)
    {
        if (action != null)
            onShutdown.AddListener(action);
    }
}
